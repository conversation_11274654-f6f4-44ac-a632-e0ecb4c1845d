﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.Companies;

[DataContract]
public class CompanyModel
{
    /// <summary>
    /// Company type
    /// </summary>
    /// <value>Company type</value>
    [DataMember(Name="type", EmitDefaultValue=false)]
    public CompanyType? Type { get; set; }
    
    /// <summary>
    /// Gets or Sets Status
    /// </summary>
    [DataMember(Name="status", EmitDefaultValue=false)]
    public CompanyStatus? Status { get; set; }

    [JsonPropertyName("bankAccounts")]
    public string?[] BankAccounts { get; set; } = null!;

    /// <summary>
    /// Id of company
    /// </summary>
    /// <value>Id of company</value>
    [DataMember(Name="id", EmitDefaultValue=false)]
    public string Id { get; set; } = null!;

    /// <summary>
    /// Company creation time.
    /// </summary>
    /// <value>Company creation time.</value>
    [DataMember(Name="createdAt", EmitDefaultValue=false)]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last update time. In the time of creation, equals to createdAt.
    /// </summary>
    /// <value>Last update time. In the time of creation, equals to createdAt.</value>
    [DataMember(Name="updatedAt", EmitDefaultValue=false)]
    public DateTime UpdatedAt { get; set; }
    
    /// <summary>
    /// Name of company
    /// </summary>
    /// <value>Name of company</value>
    [DataMember(Name="name", EmitDefaultValue=false)]
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gets or Sets Entity
    /// </summary>
    [DataMember(Name="entity", EmitDefaultValue=false)]
    public string Entity { get; set; } = null!;

    /// <summary>
    /// Gets or Sets LegalName
    /// </summary>
    [DataMember(Name="legalName", EmitDefaultValue=false)]
    public string LegalName { get; set; } = null!;

    /// <summary>
    /// Gets or Sets Website
    /// </summary>
    [DataMember(Name="website", EmitDefaultValue=false)]
    public string Website { get; set; } = null!;

    /// <summary>
    /// Gets or Sets Address
    /// </summary>
    [DataMember(Name="address", EmitDefaultValue=false)]
    public AddressModel Address { get; set; } = null!;

    /// <summary>
    /// Phone number
    /// </summary>
    /// <value>Phone number</value>
    [DataMember(Name="phone", EmitDefaultValue=false)]
    public string Phone { get; set; } = null!;

    /// <summary>
    /// Email address
    /// </summary>
    /// <value>Email address</value>
    [DataMember(Name="email", EmitDefaultValue=false)]
    public string Email { get; set; } = null!;

    /// <summary>
    /// Indicates whether item is business
    /// </summary>
    /// <value>Indicates whether item is business</value>
    [DataMember(Name="isBusiness", EmitDefaultValue=false)]
    public bool? IsBusiness { get; set; }
    
    /// <summary>
    /// Company Settings Model
    /// </summary>
    /// <value>Indicates whether item is business</value>
    [DataMember(Name="settings", EmitDefaultValue=false)]
    public CompanySettingsModel Settings { get; set; } = null!;
}