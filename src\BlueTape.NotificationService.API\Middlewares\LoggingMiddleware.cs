﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.NotificationService.Domain.Constants;
using Serilog.Context;

namespace BlueTape.NotificationService.API.Middlewares;

public class LoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public LoggingMiddleware(RequestDelegate _next, IHttpContextAccessor httpContextAccessor)
    {
        this._next = _next;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task Invoke(HttpContext context, ITraceIdAccessor traceIdAccessor)
    {
        var correlationId = context.Request.Headers[ConfigurationKeys.TraceIdHeaderName];

        if (string.IsNullOrEmpty(correlationId))
            correlationId = _httpContextAccessor.HttpContext!.TraceIdentifier;

        traceIdAccessor.TraceId = correlationId.ToString();
        
        using (GlobalLogContext.PushProperty("Path", context.Request.Path))
        using (GlobalLogContext.PushProperty("Method", context.Request.Method))
        using (GlobalLogContext.PushProperty("BlueTapeCorrelationId", correlationId))
        {
            await _next.Invoke(context);
        }
    }
}