﻿using BlueTape.MongoDB;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using System.Diagnostics.CodeAnalysis;
using BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;

namespace BlueTape.NotificationService.DataAccess.Mongo;

[ExcludeFromCodeCoverage]
public class MongoDbContext : MongoAzDbContext
{
    public MongoDbContext(IConfiguration configuration, ILogger<MongoDbContext> logger) : base(configuration, logger)
    {
    }
    
    public IMongoCollection<SystemNotificationDocument> SystemNotifications => Collection<SystemNotificationDocument>("systemNotifications");
}
