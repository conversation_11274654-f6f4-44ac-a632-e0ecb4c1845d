﻿using System.Text.Json.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.Users;

public class UserInformationModel
{
    [JsonPropertyName("subUserId")]
    public string SubUserId { get; set; } = null!;

    [JsonPropertyName("userRole")]
    public string UserRole { get; set; } = null!;

    [JsonPropertyName("companyId")]
    public string CompanyId { get; set; } = null!;

    [JsonPropertyName("phone")]
    public string Phone { get; set; } = null!;
}