using System.Text.Json.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.Customers;

public class CustomerConnectorModel
{
    [JsonPropertyName("connectorCustomerId")]
    public string ConnectorCustomerId { get; set; } = null!;

    [JsonPropertyName("integrationId")]
    public string IntegrationId { get; set; } = null!;

    [JsonPropertyName("connectorContactId")]
    public string? ConnectorContactId { get; set; }

    [JsonPropertyName("businessName")]
    public string? BusinessName { get; set; }

    [JsonPropertyName("sourceModifiedDate")]
    public DateTime SourceModifiedDate { get; set; }

    [JsonPropertyName("isPrimaryContact")]
    public bool IsPrimaryContact { get; set; }

    [JsonPropertyName("blueTapeCustomerCompanyId")]
    public string? BlueTapeCustomerCompanyId { get; set; }

    [JsonPropertyName("isCompanyType")]
    public bool IsCompanyType { get; set; }
}