﻿using BlueTape.AWSMessaging.Abstractions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Notifications.Invoicing.Abstractions;
using BlueTape.Notifications.Invoicing.Models;
using BlueTape.Notifications.Invoicing.Models.Companies;
using BlueTape.Notifications.Invoicing.Models.Customers;
using BlueTape.Notifications.Invoicing.Models.Invoices;
using BlueTape.Notifications.Invoicing.Models.Users;
using Microsoft.Extensions.Logging;

namespace BlueTape.Notifications.Invoicing.Services;

public class NotificationProcessor : INotificationProcessor
{
    private readonly ISqsEndpoint<NewInvoiceConversationRequest> _newInvoiceConversationRequestEndpoint;
    private readonly ICompaniesClient _companiesClient;
    private readonly IInvoicesClient _invoicesClient;
    private readonly IAuthenticationClient _authenticationClient;
    private readonly ITraceIdAccessor _traceIdAccessor;
    private readonly ILogger<NotificationProcessor> _logger;

    public NotificationProcessor(ISqsEndpoint<NewInvoiceConversationRequest> newInvoiceConversationRequestEndpoint,
        ICompaniesClient companiesClient,
        IInvoicesClient invoicesClient,
        IAuthenticationClient authenticationClient,
        ITraceIdAccessor traceIdAccessor,
        ILogger<NotificationProcessor> logger)
    {
        _newInvoiceConversationRequestEndpoint = newInvoiceConversationRequestEndpoint;
        _companiesClient = companiesClient;
        _invoicesClient = invoicesClient;
        _authenticationClient = authenticationClient;
        _traceIdAccessor = traceIdAccessor;
        _logger = logger;
    }

    public async Task ProcessAsync(NewInvoiceRequest invoiceRequest, CancellationToken cancellationToken = default)
    {
        var invoice = await _invoicesClient.GetByIdAsync(invoiceRequest.InvoiceId, cancellationToken);
        if (invoice is null)
        {
            _logger.LogWarning("Unable to find invoiceId: {InvoiceId}", invoiceRequest.InvoiceId);
            return;
        }
        invoice.Customer = await _companiesClient.GetCustomerByIdAsync(invoice.CustomerAccountId,cancellationToken);

        if (invoice.Customer is null)
        {
            _logger.LogWarning("Unable to find customerId: {InvoiceId}", invoiceRequest.InvoiceId);
            return;
        }

        var request = await GetPhoneNumbersForNotification(invoice, cancellationToken);
        
        if(string.IsNullOrEmpty(request.Phone) && !request.DefaultNotificationPhoneList.Any())
            return;

        await _newInvoiceConversationRequestEndpoint.SendAsync(request, _traceIdAccessor.TraceId, cancellationToken);
    }

    private async Task<NewInvoiceConversationRequest> GetPhoneNumbersForNotification(InvoiceModel invoice, CancellationToken cancellationToken)
    {
        var targetPhone = string.Empty;
        
        var defaultNotificationPhoneListTask = GetPhonesForSimpleNotification(invoice, cancellationToken);
        var userInfoTask = _authenticationClient
            .GetUserInfoByPhoneOrEmailAsync(invoice.Customer!.CellPhoneNumber, invoice.Customer.EmailAddress, cancellationToken);

        await Task.WhenAll(defaultNotificationPhoneListTask, userInfoTask);
        
        var defaultNotificationPhoneList = await defaultNotificationPhoneListTask;
        var userInfo = await userInfoTask;
        
        if (userInfo is null)
        {
            _logger.LogInformation("User not found with login equals to phone: {phone} or email: {EmailAddress}. Sending default notification",
                invoice.Customer.CellPhoneNumber, invoice.Customer.EmailAddress);

            if (!string.IsNullOrEmpty(invoice.Customer.CellPhoneNumber))
                defaultNotificationPhoneList.Add(invoice.Customer.CellPhoneNumber);
        }
        
        if (userInfo != null && string.IsNullOrEmpty(userInfo.Phone))
        {
            _logger.LogWarning("User was found with login equals to phone: {phone} or email: {EmailAddress}, but registered user don't have phone number. Sending default notification",
                invoice.Customer.CellPhoneNumber, invoice.Customer.EmailAddress);
            
            if (!string.IsNullOrEmpty(invoice.Customer.CellPhoneNumber))
                defaultNotificationPhoneList.Add(invoice.Customer.CellPhoneNumber);
        }

        if (userInfo != null && !string.IsNullOrEmpty(userInfo.Phone))
        {
            var userCanPaySupplier = await CheckPaymentAvailabilityAsync(userInfo, invoice.CompanyId, cancellationToken);

            if (userCanPaySupplier)
            {
                _logger.LogInformation("Initiating POT request for {phone} and it's invoiceId: {InvoiceId}", 
                    targetPhone, invoice.Id);
                targetPhone = userInfo.Phone;
            }
            else
            {
                _logger.LogInformation("User with phone: {phone} don't have available payment methods allowed by supplier. Send default sms.",
                    userInfo.Phone);
                defaultNotificationPhoneList.Add(userInfo.Phone);
            }
        }

        return new NewInvoiceConversationRequest()
        {
            InvoiceId = invoice.Id,
            Phone = targetPhone,
            DefaultNotificationPhoneList = defaultNotificationPhoneList
        };
    }

    public async Task<List<string>> GetPhonesForSimpleNotification(InvoiceModel invoice, CancellationToken cancellationToken)
    {
        var allSimpleCustomers = new List<CustomerModel>();
        var tasks = new List<Task>();
        Task<CustomerModel[]?> invoicesContactsTask = null!;
        
        var billingContactsTasks = _companiesClient
            .GetCustomerBillingContactsAsync(invoice.Customer!.BlueTapeCustomerId, cancellationToken);
        tasks.Add(billingContactsTasks);

        if (invoice.Contacts != null && invoice.Contacts.Any())
        {
            invoicesContactsTask = _companiesClient
                .GetCustomerByIdsAsync(invoice.Contacts, cancellationToken);
            tasks.Add(invoicesContactsTask);
        }
        
        await Task.WhenAll(tasks);

        var billingContacts = await billingContactsTasks;
        var invoicesContacts = invoicesContactsTask != null ? (await invoicesContactsTask)! : ArraySegment<CustomerModel>.Empty;

        if (billingContacts != null && billingContacts.Any()) allSimpleCustomers.AddRange(billingContacts);
        allSimpleCustomers.AddRange(invoicesContacts);

        return allSimpleCustomers
            .GroupBy(x => x.CellPhoneNumber)
            .Where(x => x.Any(y => !string.IsNullOrEmpty(y.CellPhoneNumber)))
            .Select(x => x.First(y => !string.IsNullOrEmpty(y.CellPhoneNumber)).CellPhoneNumber)
            .ToList()!;
    }

    public async Task<bool> CheckPaymentAvailabilityAsync(UserInformationModel userInformationModel, string supplierCompanyId, CancellationToken cancellationToken)
    {
        var companies = await _companiesClient.GetCompaniesByIdsAsync
            (new []{supplierCompanyId, userInformationModel.CompanyId}, cancellationToken);

        if (companies is null)
        {
            _logger.LogWarning("Cant find supplier companyId: {supplierCompanyId} and user companyId: {userCompanyId}",
                supplierCompanyId, userInformationModel.CompanyId);
            return false;
        }
        
        var supplierCompany = companies.FirstOrDefault(x => x.Id.Equals(supplierCompanyId));
        var userCompany = companies.FirstOrDefault(x => x.Id.Equals(userInformationModel.CompanyId));

        if (supplierCompany is null)
        {
            _logger.LogWarning("Cant find supplier companyId: {supplierCompanyId}", supplierCompanyId);
            return false;
        } 
        if (userCompany is null)
        {
            _logger.LogWarning("Cant find user companyId: {userCompanyId}", userInformationModel.CompanyId);
            return false;
        } 
        if (userCompany.BankAccounts is null || !userCompany.BankAccounts.Any()) return false;
        
        var userBankAccounts = await _companiesClient.GetBankAccountsByIdsAsync(userCompany.BankAccounts, cancellationToken);

        if (userBankAccounts is null || !userBankAccounts.Any())
        {
            _logger.LogWarning("Cant find user bankAccountIds: {BankAccounts}", userCompany.BankAccounts.ToString());
            return false;
        } 
        
        var supplierSupportCard = supplierCompany.Settings.SupportCardPayments();
        var userHasBankPayment = userBankAccounts.Any(x => x.PaymentMethodType != null && x.PaymentMethodType.Equals(CompanyConstants.BankPaymentType));
        var userHasCardPayment = userBankAccounts.Any(x => x.PaymentMethodType != null && x.PaymentMethodType.Equals(CompanyConstants.CardPaymentType));

        return userHasBankPayment || (supplierSupportCard && userHasCardPayment);
    }
}