﻿using System.Diagnostics.CodeAnalysis;
using BlueTape.NotificationService.API.Middlewares;
namespace BlueTape.NotificationService.API.Extensions;

[ExcludeFromCodeCoverage]
public static class WebApplicationExtensions
{
    public static WebApplication IncludeMiddlewares(this WebApplication app)
    {
        app.UseMiddleware<LoggingMiddleware>();
        app.UseMiddleware<ExceptionMiddleware>();

        return app;
    }
}