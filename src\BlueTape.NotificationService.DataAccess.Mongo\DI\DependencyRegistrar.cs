﻿using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics.CodeAnalysis;
using BlueTape.NotificationService.DataAccess.Mongo.Abstractions;
using BlueTape.NotificationService.DataAccess.Mongo.Repositories;

namespace BlueTape.NotificationService.DataAccess.Mongo.DI;

[ExcludeFromCodeCoverage]
public static class DependencyRegistrar
{
    public static void AddMongoDataAccessDependencies(this IServiceCollection services)
    {
        services.AddSingleton<MongoDbContext>();
        services.AddTransient<ISystemNotificationRepository, SystemNotificationRepository>();
    }
}