# Generic variables
variable "location" {
  type    = string
  default = "West US"
}

variable "environment" {
  type    = string
  default = "qa"
}

variable "application_name" {
  type = map(any)
  default = {
    full = "notification-service",
    slug = "notification"
  }
}

variable "keyvault_unique_name" {
  type    = string
  default = "keyvault-qa-b2c7f314826"
}

variable "acr_password" {
  description = "ACR password"
  default = ""
}

variable "client_id" {
  description = "ARM client id"
  default = ""
}

variable "tenant_id" {
  description = "ARM Tenant id"
  default = ""
}

variable "client_secret" {
  description = "ARM client secret"
  default = ""
}

variable "container_registry_name" {
  type    = string
  default = "containerRegistryBTQa"
}

variable "image_version" {
  type    = string
  default = "latest"
}

variable "lp_aws_account" {
  description = "AWS account id"
  default = ""
}

variable "aws_access_key_id" {
  description = "AWS access key id"
  default = ""
}

variable "aws_secret_access_key" {
  description = "AWS secret access key"
  default = ""
}

variable "aws_default_region" {
  description = "AWS default region"
  default = "us-west-1"
}

variable "environment_camelcase_map" {
  type = map(any)
  default = {
    dev  = "Dev"
    qa   = "Qa"
    beta = "Beta"
    prod = "Prod"
  }
}

variable "functions_worker_runtime" {
    type    = string
    default = "dotnet-isolated"
}
variable "dotnet_version" {
    type    = string
    default = "8.0"
}

variable "function_extension_version" {
    type    = string
    default = "~4"
}

data "terraform_remote_state" "core" {
  backend = "azurerm"
  config = {
    resource_group_name  = var.environment
    storage_account_name = "tfstatebtqa03ca9fde916"
    container_name       = "tfstate-core-infra"
    key                  = "terraform.tfstate"
  }
}