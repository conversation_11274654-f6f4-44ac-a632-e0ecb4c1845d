{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"AWSSDK": "Warning", "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "BlueTape.Services.Utilities.AWS": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.DataProtection": "Error", "Microsoft.EntityFrameworkCore.Infrastructure": "Warning", "System.Net.Http.HttpClient": "Warning"}}}, "NotificationQueueOptions": {"InvoiceNotificationTemplateTopicName": "user-interaction-service-invoicing-dev.fifo"}, "Services": {"InvoicesApi": "https://dev-api.bluetape.com/invoiceService/", "CompaniesApi": "https://dev-api.bluetape.com/companyService/", "AuthenticationApi": "https://dev-api.bluetape.com/authentication/"}, "BlueTapeOptions": {"AwsSecretName": "bluetape_keys_dev"}}