using System.Runtime.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.Invoices.Enums;

public enum InvoiceModelStatus
{
    [EnumMember(Value = @"draft")]
    Draft = 0,

    [EnumMember(Value = @"placed")]
    Placed = 1,

    [EnumMember(Value = @"paid")]
    Paid = 2,

    [EnumMember(Value = @"cancelled")]
    Cancelled = 3,

    [EnumMember(Value = @"dismissed")]
    Dismissed = 4,
}
