using BlueTape.Services.Utilities.AspNetCore;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Notifications.Invoicing.Extensions;

public static class ServiceCollectionExtensions
{
    public static IHttpClientBuilder AddTypedHttpClient<TInterface, TImplementation>(this IServiceCollection services)
        where TInterface : class where TImplementation : class, TInterface
    {
        return services
            .AddHttpClient<TInterface, TImplementation>()
            .AddPolicyHandler(
                (svc, _) => HttpClientPolicies.GetGenericRetryPolicy<TImplementation>(svc))
            .AddHttpMessageHandler<RequestResponseLoggingDelegatingHandler>();
    }
}
