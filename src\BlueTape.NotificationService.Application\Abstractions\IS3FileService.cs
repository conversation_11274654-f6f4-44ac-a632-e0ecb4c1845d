using BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;

namespace BlueTape.NotificationService.Application.Abstractions;

public interface IS3FileService
{
    Task<byte[]> DownloadFileAsync(string bucketName, string key, CancellationToken cancellationToken);
    Task<Dictionary<string, byte[]>> DownloadFilesAsync(List<S3AttachmentReferenceDocument> attachmentReferences, CancellationToken cancellationToken);
    Task<bool> FileExistsAsync(string bucketName, string key, CancellationToken cancellationToken);
}
