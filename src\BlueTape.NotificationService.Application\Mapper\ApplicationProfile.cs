using AutoMapper;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;

namespace BlueTape.NotificationService.Application.Mapper;

public class ApplicationProfile : Profile
{
    public ApplicationProfile()
    {
        CreateMap<SystemNotificationDto, SystemNotificationDocument>()
            .ForMember(x => x.CreatedAt, y => y.Ignore())
            .ForMember(x => x.UpdatedAt, y => y.Ignore())
            .ForMember(x => x.Id, y => y.Ignore());

        CreateMap<UserUiReviewPayloadDto, UserUiReviewPayloadDocument>();

        CreateMap<EmailReceiverDataDto, EmailReceiverData>();
        CreateMap<S3AttachmentReferenceDto, S3AttachmentReferenceDocument>();

        CreateMap<NotificationChannelDto<UserUiReviewPayloadDto>, NotificationChannelDocument<UserUiReviewPayloadDocument>>()
            .ForMember(x => x.DeliveryId, y => y.Ignore())
            .ForMember(x => x.IsDelivered, y =>
                y.MapFrom(src => false));

        CreateMap<EmailPayloadDto, EmailPayloadDocument>();
        CreateMap<NotificationChannelDto<EmailPayloadDto>, NotificationChannelDocument<EmailPayloadDocument>>()
            .ForMember(x => x.DeliveryId, y => y.Ignore())
            .ForMember(x => x.IsDelivered, y =>
                y.MapFrom(src => false));



        CreateMap<BlueTapeBackOfficePayloadDto, BlueTapeBackOfficePayloadDocument>();
        CreateMap<NotificationChannelDto<BlueTapeBackOfficePayloadDto>, NotificationChannelDocument<BlueTapeBackOfficePayloadDocument>>()
            .ForMember(x => x.DeliveryId, y => y.Ignore())
            .ForMember(x => x.IsDelivered, y =>
                y.MapFrom(src => false));

        CreateMap<SlackPayloadDto, SlackPayloadDocument>();

    }
}