using System.Runtime.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.Invoices;

// ReSharper disable once ClassNeverInstantiated.Global
public class InvoiceDocumentModel
{
    /// <summary>
    /// Name of document.
    /// </summary>
    [DataMember(Name="name", EmitDefaultValue=false)]
    public string Name { get; set; } = null!;

    /// <summary>
    /// Url of document.
    /// </summary>
    [DataMember(Name="url", EmitDefaultValue=false)]
    public string Url { get; set; } = null!;
}
