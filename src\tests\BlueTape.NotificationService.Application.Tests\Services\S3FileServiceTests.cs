using Amazon.S3;
using Amazon.S3.Model;
using BlueTape.NotificationService.Application.Services;
using BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;
using Microsoft.Extensions.Logging;
using Moq;
using System.Net;
using Xunit;

namespace BlueTape.NotificationService.Application.Tests.Services;

public class S3FileServiceTests
{
    private readonly Mock<IAmazonS3> _mockS3Client;
    private readonly Mock<ILogger<S3FileService>> _mockLogger;
    private readonly S3FileService _s3FileService;

    public S3FileServiceTests()
    {
        _mockS3Client = new Mock<IAmazonS3>();
        _mockLogger = new Mock<ILogger<S3FileService>>();
        _s3FileService = new S3FileService(_mockS3Client.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task DownloadFileAsync_ShouldReturnFileContent_WhenFileExists()
    {
        // Arrange
        var bucketName = "test-bucket";
        var key = "test-file.pdf";
        var expectedContent = "test file content"u8.ToArray();
        
        var mockResponse = new GetObjectResponse
        {
            ResponseStream = new MemoryStream(expectedContent)
        };

        _mockS3Client
            .Setup(x => x.GetObjectAsync(It.IsAny<GetObjectRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockResponse);

        // Act
        var result = await _s3FileService.DownloadFileAsync(bucketName, key, CancellationToken.None);

        // Assert
        Assert.Equal(expectedContent, result);
        _mockS3Client.Verify(x => x.GetObjectAsync(
            It.Is<GetObjectRequest>(req => req.BucketName == bucketName && req.Key == key),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task DownloadFilesAsync_ShouldReturnMultipleFiles_WhenAllFilesExist()
    {
        // Arrange
        var attachmentReferences = new List<S3AttachmentReferenceDocument>
        {
            new()
            {
                BucketName = "test-bucket",
                Key = "file1.pdf",
                FileName = "Document1.pdf",
                ContentType = "application/pdf"
            },
            new()
            {
                BucketName = "test-bucket",
                Key = "file2.txt",
                FileName = "Document2.txt",
                ContentType = "text/plain"
            }
        };

        var file1Content = "file1 content"u8.ToArray();
        var file2Content = "file2 content"u8.ToArray();

        _mockS3Client
            .SetupSequence(x => x.GetObjectAsync(It.IsAny<GetObjectRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new GetObjectResponse { ResponseStream = new MemoryStream(file1Content) })
            .ReturnsAsync(new GetObjectResponse { ResponseStream = new MemoryStream(file2Content) });

        // Act
        var result = await _s3FileService.DownloadFilesAsync(attachmentReferences, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.True(result.ContainsKey("Document1.pdf"));
        Assert.True(result.ContainsKey("Document2.txt"));
        Assert.Equal(file1Content, result["Document1.pdf"]);
        Assert.Equal(file2Content, result["Document2.txt"]);
    }

    [Fact]
    public async Task FileExistsAsync_ShouldReturnTrue_WhenFileExists()
    {
        // Arrange
        var bucketName = "test-bucket";
        var key = "test-file.pdf";

        _mockS3Client
            .Setup(x => x.GetObjectMetadataAsync(It.IsAny<GetObjectMetadataRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new GetObjectMetadataResponse());

        // Act
        var result = await _s3FileService.FileExistsAsync(bucketName, key, CancellationToken.None);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task FileExistsAsync_ShouldReturnFalse_WhenFileDoesNotExist()
    {
        // Arrange
        var bucketName = "test-bucket";
        var key = "non-existent-file.pdf";

        _mockS3Client
            .Setup(x => x.GetObjectMetadataAsync(It.IsAny<GetObjectMetadataRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new AmazonS3Exception("Not Found") { StatusCode = HttpStatusCode.NotFound });

        // Act
        var result = await _s3FileService.FileExistsAsync(bucketName, key, CancellationToken.None);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task DownloadFilesAsync_ShouldReturnEmptyDictionary_WhenNoAttachmentReferences()
    {
        // Act
        var result = await _s3FileService.DownloadFilesAsync(null, CancellationToken.None);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task DownloadFilesAsync_ShouldReturnEmptyDictionary_WhenEmptyAttachmentReferences()
    {
        // Act
        var result = await _s3FileService.DownloadFilesAsync(new List<S3AttachmentReferenceDocument>(), CancellationToken.None);

        // Assert
        Assert.Empty(result);
    }
}
