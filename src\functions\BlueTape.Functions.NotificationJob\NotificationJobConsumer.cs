using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Azure.Messaging.ServiceBus;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Notification.Sender.Constants;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.NotificationService.Domain.Constants;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using BlueTape.NotificationService.Application.Abstractions;

namespace BlueTape.Functions.NotificationJob;

public class NotificationJobConsumer(
    ILogger<NotificationJobConsumer> logger,
    ISlackNotificationService slackNotificationService,
    INotificationProcessor notificationProcessor, 
    ITraceIdAccessor traceIdAccessor)
{
    private readonly JsonSerializerOptions _serializerOptions = new(JsonSerializerDefaults.Web)
    {
        Converters = { new JsonStringEnumConverter() },
        PropertyNameCaseInsensitive = true,
        WriteIndented = false
    };

    [Function("NotificationJobConsumer")]
    public async Task Run([
        ServiceBusTrigger($"%{NotificationConstants.NotificationJobQueueName}%",
                Connection = $"{NotificationConstants.NotificationJobQueueConnection}")]
            ServiceBusReceivedMessage message,
            ServiceBusMessageActions messageActions,
            CancellationToken ct)
    {
        traceIdAccessor.TraceId = $"{Guid.NewGuid()}-{nameof(NotificationJobConsumer)}";

        using (GlobalLogContext.PushProperty("functionName", "NotificationJobConsumer"))
        using (GlobalLogContext.PushProperty("BlueTapeCorrelationId", traceIdAccessor.TraceId))
        {
            logger.LogInformation("Notification consumer, got message from the queue: Session: {sessionId}, \n Message: {messageId}, \n Body: {@message}", message.SessionId, message.MessageId, Encoding.UTF8.GetString(message.Body));

            try
            {
                var parsedMessage = message.Body.ToObjectFromJson<SystemNotificationDto?>(_serializerOptions);

                logger.LogInformation("Notification consumer, parsed message: {@parsedMessage}", parsedMessage);

                if (parsedMessage is null)
                {
                    logger.LogError(
                        "Unable to process message with empty body or invalid fields. Session: {sessionId}, \n Message: {messageId}, \n Body: {@message}",
                        message.SessionId, message.MessageId, Encoding.UTF8.GetString(message.Body));
                    throw new ArgumentNullException(nameof(message));
                }
                
                await notificationProcessor.Process(parsedMessage, ct);
                logger.LogInformation($"Notification event was processed succesfully");
            }
            catch (DomainException ex)
            {
                await slackNotificationService.Notify(ex.GetSlackEventMessageBody(
                    EnvironmentExtensions.GetExecutionEnvironment(), 
                    ConfigurationKeys.ProjectValue),
                    ct);
                logger.LogError("A business logic exception occurred while processing the message: {messageId}, \n  Exception: {ex}", message.MessageId, ex);
                await messageActions.DeadLetterMessageAsync(message, cancellationToken: ct);
                throw;
            }
            catch (Exception ex)
            {
                await slackNotificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), ConfigurationKeys.ProjectValue), ct);
                logger.LogError("A business logic exception occurred while processing the message: {messageId}, \n  Exception: {ex}", message.MessageId, ex);
                await messageActions.DeadLetterMessageAsync(message, cancellationToken: ct);
                throw;
            }
        }
    }
}
