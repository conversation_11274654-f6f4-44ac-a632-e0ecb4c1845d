using Amazon;
using Amazon.Extensions.NETCore.Setup;
using Amazon.KeyManagementService;
using Amazon.S3;
using Amazon.SecretsManager;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.LambdaBase;
using BlueTape.NotificationService.Application.DI;
using BlueTape.NotificationService.Domain.Constants;
using BlueTape.NotificationService.Domain.Enrichers;
using BlueTape.Services.Utilities.Options;
using BlueTape.Utilities.Constants;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;

var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureAppConfiguration(x =>
    {
        x.SetBasePath(Environment.CurrentDirectory);
        x.AddJsonFile("appsettings.json", optional: false);
        x.AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment)}.json", optional: true);
        var keyVaultUri = new Uri(Environment.GetEnvironmentVariable(ConfigConstants.KeyVaultUri) ??
                                  throw new VariableNullException(ConfigConstants.KeyVaultUri));
        var azureCredentials = new DefaultAzureCredential();
        x.AddAzureKeyVault(keyVaultUri, azureCredentials, new AzureKeyVaultConfigurationOptions
        {
            ReloadInterval = TimeSpan.FromMinutes(EnvironmentConstants.MinutestKeyVaultReload)
        });
        x.AddEnvironmentVariables();
    })
    .UseSerilog((hostingContext, loggerConfiguration) =>
    {
        var config = hostingContext.Configuration;
        loggerConfiguration
            .ReadFrom.Configuration(hostingContext.Configuration)
            .Enrich.FromGlobalLogContext()
            .Enrich.WithProperty("ProjectName", ConfigurationKeys.ProjectValue)
            .Enrich.WithProperty("EnvironmentName", hostingContext.HostingEnvironment.EnvironmentName)
            .Enrich.WithProperty("ContentRootPath", hostingContext.HostingEnvironment.ContentRootPath)
            .Enrich.With<StackTraceEnricher>()
            .WriteTo.ApplicationInsights(config.GetSection(ConfigConstants.AppInsightsConnection).Value,
                TelemetryConverter.Traces);

        loggerConfiguration.WriteTo.Console();
    })
    .ConfigureServices((hostBuilderContext, services) =>
    {
        var configuration = hostBuilderContext.Configuration; services.AddOptions();
        services.Configure<BlueTapeOptions>(configuration.GetSection(nameof(BlueTapeOptions)));

        services.AddDefaultAWSOptions(configuration.GetAWSOptions());
        services.AddAWSService<IAmazonS3>(new AWSOptions { Region = RegionEndpoint.USWest1 });
        services.AddAWSService<IAmazonSecretsManager>(new AWSOptions { Region = RegionEndpoint.USWest1 });
        services.AddAWSService<IAmazonKeyManagementService>(new AWSOptions { Region = RegionEndpoint.USWest1 });

        services.AddApplicationDependencies(configuration);

        services.AddScoped<ITraceIdAccessor, LambdaTraceIdAccessor>();

        var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
        if (!string.IsNullOrEmpty(env) && !env.Equals(EnvironmentConstants.Local))
        {
            var serviceProvider = services.BuildServiceProvider();
            var keyVaultService = serviceProvider.GetRequiredService<IKeyVaultService>();
            var appInsightsConnectionString = keyVaultService.GetSecret(InfrastructureConstants.AppInsightsConnection).GetAwaiter().GetResult();

            services.AddApplicationInsightsTelemetryWorkerService(x => x.ConnectionString = appInsightsConnectionString);
            services.ConfigureFunctionsApplicationInsights();
        }

        services.Configure<LoggerFilterOptions>(options =>
        {
            var toRemove = options.Rules.FirstOrDefault(rule => rule.ProviderName
                                                                == "Microsoft.Extensions.Logging.ApplicationInsights.ApplicationInsightsLoggerProvider");

            if (toRemove is not null)
            {
                options.Rules.Remove(toRemove);
            }
        });
    })
    .Build();

host.Run();
