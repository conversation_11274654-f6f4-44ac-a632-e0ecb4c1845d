﻿{
  "$schema": "https://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:52025",
      "sslPort": 44378
    }
  },
  "profiles": {
    "BlueTape.NotificationService.API BETA": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7237;http://localhost:5237",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "beta",
        "AWS_REGION": "us-west-1",
        "LP_AWS_ACCOUNT": "************",
        "Branch": "Local",
        "LOGZIO_TOKEN": "PJiLirYZjrLhGExyBanGEwrtdaZBvUIx",
        "KEYVAULT_URI": "https://keyvault-beta-10ea6a9eb6.vault.azure.net/"
      }
    },
    "BlueTape.NotificationService.API QA": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7237;http://localhost:5237",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "qa",
        "AWS_REGION": "us-west-1",
        "LP_AWS_ACCOUNT": "************",
        "Branch": "Local",
        "LOGZIO_TOKEN": "PJiLirYZjrLhGExyBanGEwrtdaZBvUIx",
        "KEYVAULT_URI": "https://keyvault-qa-b2c7f314826.vault.azure.net/"
      }
    }
  }
}
