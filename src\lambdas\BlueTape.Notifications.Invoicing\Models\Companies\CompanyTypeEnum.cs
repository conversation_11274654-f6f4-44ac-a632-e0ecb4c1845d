using System.Runtime.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.Companies;

/// <summary>
/// Company type
/// </summary>
/// <value>Company type</value>
public enum CompanyType
{
    /// <summary>
    /// Enum Supplier for value: supplier
    /// </summary>
    [EnumMember(Value = "supplier")]
    Supplier = 1,
    /// <summary>
    /// Enum Contractor for value: contractor
    /// </summary>
    [EnumMember(Value = "contractor")]
    Contractor = 2        
}