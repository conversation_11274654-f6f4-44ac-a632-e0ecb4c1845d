using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace BlueTape.NotificationService.DataAccess.Mongo.Serializers;

public class EnumListAsStringSerializer<TEnum> : SerializerBase<List<TEnum>> where TEnum : struct, Enum
{
    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, List<TEnum> value)
    {
        context.Writer.WriteStartArray();
        foreach (var item in value)
        {
            context.Writer.WriteString(item.ToString());
        }
        context.Writer.WriteEndArray();
    }

    public override List<TEnum> Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        var list = new List<TEnum>();

        context.Reader.ReadStartArray();
        while (context.Reader.ReadBsonType() != BsonType.EndOfDocument)
        {
            var enumString = context.Reader.ReadString();
            if (Enum.TryParse(enumString, out TEnum parsedEnum))
            {
                list.Add(parsedEnum);
            }
        }
        context.Reader.ReadEndArray();

        return list;
    }
}
