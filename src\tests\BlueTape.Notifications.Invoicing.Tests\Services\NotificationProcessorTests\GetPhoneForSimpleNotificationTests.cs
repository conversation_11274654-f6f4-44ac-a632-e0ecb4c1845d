﻿using System;
using System.Threading;
using System.Threading.Tasks;
using AutoFixture;
using BlueTape.AWSMessaging.Abstractions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Notifications.Invoicing.Abstractions;
using BlueTape.Notifications.Invoicing.Models;
using BlueTape.Notifications.Invoicing.Models.Customers;
using BlueTape.Notifications.Invoicing.Models.Invoices;
using BlueTape.Notifications.Invoicing.Services;
using BlueTape.UnitTests.Base.Extensions;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlueTape.Notifications.Invoicing.Tests.Services.NotificationProcessorTests;

public class GetPhoneForSimpleNotificationTests
{
    private readonly Mock<ICompaniesClient> _companiesClient = new();
    private readonly Mock<IInvoicesClient> _invoiceClient = new();
    private readonly Mock<IAuthenticationClient> _authenticationClient = new();
    private readonly Mock<ITraceIdAccessor> _traceIdAccessor = new();
    private readonly Mock<ILogger<NotificationProcessor>> _loggerMock = new();
    private readonly Mock<ISqsEndpoint<NewInvoiceConversationRequest>> _sqs = new();
    private readonly Fixture _fixture = new();

    private NotificationProcessor GetService()
    {
        return new NotificationProcessor(
            _sqs.Object,
            _companiesClient.Object,
            _invoiceClient.Object,
            _authenticationClient.Object,
            _traceIdAccessor.Object,
            _loggerMock.Object);
    }
    
    [Fact]
    public async Task GetPhonesForSimpleNotification_OnlyBillingContacts()
    {
        var invoice = new InvoiceModel()
        {
            Customer = _fixture.Create<CustomerModel>()
        };
        var billingContacts = new CustomerModel[]
        {
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = Guid.NewGuid().ToString()},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = Guid.NewGuid().ToString()},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "123"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "123"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "1234"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "1234"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "1234"},
        };

        _companiesClient
            .Setup(x => x.GetCustomerBillingContactsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(billingContacts);
        
        var service = GetService();
        var results = await service.GetPhonesForSimpleNotification(invoice, _fixture.Create<CancellationToken>());

        results.Should().NotBeNull();
        results.Count.Should().Be(4);
        _companiesClient.Verify(x =>
            x.GetCustomerBillingContactsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once());
        _companiesClient.Verify(x => x.GetCustomerByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()), Times.Never);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Never());
    }
    
    [Fact]
    public async Task GetPhonesForSimpleNotification_BillingContacts_And_InvoiceContacts()
    {
        var invoice = new InvoiceModel()
        {
            Customer = _fixture.Create<CustomerModel>(),
            Contacts = new []{"1", "2", "4", "4", "5", "6", "7"}
        };
        var billingContacts = new CustomerModel[]
        {
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = Guid.NewGuid().ToString()},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = Guid.NewGuid().ToString()},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "123"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "123"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "1234"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "1234"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "1234"},
        };
        
        var invoiceContacts = new CustomerModel[]
        {
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = Guid.NewGuid().ToString()},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = Guid.NewGuid().ToString()},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "0123"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "0123"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "01234"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "01234"},
            new() {BlueTapeCustomerId = Guid.NewGuid().ToString(), CellPhoneNumber = "01234"},
        };

        _companiesClient
            .Setup(x => x.GetCustomerBillingContactsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(billingContacts);
        
        _companiesClient
            .Setup(x => x.GetCustomerByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(invoiceContacts);
        
        var service = GetService();
        var results = await service.GetPhonesForSimpleNotification(invoice, _fixture.Create<CancellationToken>());

        results.Should().NotBeNull();
        results.Count.Should().Be(8);
        _companiesClient.Verify(x =>
            x.GetCustomerBillingContactsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once());
        _companiesClient.Verify(x => x.GetCustomerByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()), Times.Once);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Never());
    }
    
    [Fact]
    public async Task GetPhonesForSimpleNotification_NoBillingContacts_And_NoInvoiceContacts()
    {
        var invoice = new InvoiceModel()
        {
            Customer = _fixture.Create<CustomerModel>(),
            Contacts = new []{"1", "2", "4", "4", "5", "6", "7"}
        };
        var billingContacts = Array.Empty<CustomerModel>();
        var invoiceContacts = Array.Empty<CustomerModel>();

        _companiesClient
            .Setup(x => x.GetCustomerBillingContactsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(billingContacts);
        
        _companiesClient
            .Setup(x => x.GetCustomerByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(invoiceContacts);
        
        var service = GetService();
        var results = await service.GetPhonesForSimpleNotification(invoice, _fixture.Create<CancellationToken>());

        results.Should().NotBeNull();
        results.Count.Should().Be(0);
        _companiesClient.Verify(x =>
            x.GetCustomerBillingContactsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once());
        _companiesClient.Verify(x => x.GetCustomerByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()), Times.Once);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Never());
    }
}