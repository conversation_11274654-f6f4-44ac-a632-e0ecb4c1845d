﻿using System.Collections.Concurrent;
using System.Net;
using BlueTape.Common.ExceptionHandling.Enums;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Common.ExceptionHandling.Models;
using BlueTape.NotificationService.Domain.Constants;
using BlueTape.NotificationService.Domain.Dictionaires;
using BlueTape.Utilities.Extensions;

namespace BlueTape.NotificationService.API.Helpers;

public sealed class ExceptionToResponseMapper : IExceptionToResponseMapper
{
    private static readonly ConcurrentDictionary<Type, string> Codes = new();

    public (List<ErrorModel>, HttpStatusCode) Map(Exception exception)
    {
        Exception exceptionToHandle = exception;

        if (exception.InnerException is DomainException)
            exceptionToHandle = exception.InnerException!;

        var (res, code) = exceptionToHandle switch
        {
            DomainException ex => (new List<ErrorModel>
            {
                new()
                {
                    ErrorType = ErrorCodes.ValidationErrors.ContainsKey(GetCode(ex))
                        ? ErrorType.ValidationError
                        : ErrorType.BusinessLogicError,
                    Code = GetCode(ex),
                    Reason = ex.Message
                }
            }, ex.HttpStatusCode),
            Exception ex => (new List<ErrorModel>
            {
                new()
                {
                    ErrorType = ErrorType.Default,
                    Code = GetCode(ex),
                    Reason = ex.Message,
                }
            }, HttpStatusCode.InternalServerError),
            _ => (new List<ErrorModel>
            {
                new()
                {
                    ErrorType = ErrorType.InternalError,
                    Code = "error",
                    Reason = ExceptionConstants.GeneralExceptionMessage
                }
            }, HttpStatusCode.InternalServerError)
        };

        return (res, code);
    }

    private static string GetCode(Exception exception)
    {
        var type = exception.GetType();

        if (Codes.TryGetValue(type, out var code))
        {
            return code;
        }

        var exceptionCode = exception switch
        {
            DomainException ex when !string.IsNullOrWhiteSpace(ex.Code) => ex.Code,
            _ => type.Name.Underscore().Replace("_exception", string.Empty)
        };

        Codes.TryAdd(type, exceptionCode);

        return exceptionCode;
    }
}
