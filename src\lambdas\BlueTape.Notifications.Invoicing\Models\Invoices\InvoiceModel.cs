using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using BlueTape.Notifications.Invoicing.Models.Companies;
using BlueTape.Notifications.Invoicing.Models.Customers;
using BlueTape.Notifications.Invoicing.Models.Invoices.Enums;

namespace BlueTape.Notifications.Invoicing.Models.Invoices;

public class InvoiceModel
{
    /// <summary>
    /// Id of invoice
    /// </summary>
    [DataMember(Name="id", EmitDefaultValue=false)]
    public string Id { get; set; } = null!;

    /// <summary>
    /// Id of quote (self-reference)
    /// </summary>
    [DataMember(Name="quoteId", EmitDefaultValue=false)]
    public string QuoteId { get; set; } = null!;

    /// <summary>
    /// Invoice creation time.
    /// </summary>
    [DataMember(Name="createdAt", EmitDefaultValue=false)]
    public string CreatedAt { get; set; } = null!;

    /// <summary>
    /// Last update time. In the time of creation, equals to createdAt.
    /// </summary>
    [DataMember(Name="updatedAt", EmitDefaultValue=false)]
    public string UpdatedAt { get; set; } = null!;

    /// <summary>
    /// Invoice type
    /// </summary>
    [DataMember(Name="type", EmitDefaultValue=false)]
    [Required]
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public InvoiceModelType Type { get; set; }

    /// <summary>
    /// Number of invoice. Unique per supplier.
    /// </summary>
    [DataMember(Name="invoice_number", EmitDefaultValue=false)]
    [Required]
    public string InvoiceNumber { get; set; } = null!;

    /// <summary>
    /// Last operation id.
    /// </summary>
    [DataMember(Name="operation_id", EmitDefaultValue=false)]
    public string OperationId { get; set; } = null!;

    /// <summary>
    /// Company id.
    /// </summary>
    [DataMember(Name = "company_id", EmitDefaultValue = false)]
    public string CompanyId { get; set; } = null!;

    [DataMember(Name = "company", EmitDefaultValue = false)]
    public CompanyModel Company { get; set; } = null!;

    /// <summary>
    /// (!) Is it a company or customer id?
    /// </summary>
    [DataMember(Name = "payer_id", EmitDefaultValue = false)]
    public string PayerId { get; set; } = null!;

    /// <summary>
    /// Customer account id.
    /// </summary>
    [DataMember(Name = "customer_account_id", EmitDefaultValue = false)]
    public string CustomerAccountId { get; set; } = null!;

    [DataMember(Name="customer", EmitDefaultValue=false)]
    [Required]
    public CustomerModel? Customer { get; set; } = new CustomerModel();

    [DataMember(Name = "customerAddress", EmitDefaultValue = false)]
    public CustomerAddressModel CustomerAddress { get; set; } = null!;

    /// <summary>
    /// Material description.
    /// </summary>
    [DataMember(Name = "material_description", EmitDefaultValue = false)]
    public string MaterialDescription { get; set; } = null!;

    /// <summary>
    /// Material subtotal.
    /// </summary>
    [DataMember(Name="material_subtotal", EmitDefaultValue=false)]
    public decimal MaterialSubtotal { get; set; }

    /// <summary>
    /// Tax amount.
    /// </summary>
    [DataMember(Name="tax_amount", EmitDefaultValue=false)]
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Refunded amount.
    /// </summary>
    [DataMember(Name="refunded_amount", EmitDefaultValue=false)]
    public decimal RefundedAmount { get; set; }

    /// <summary>
    /// Invoice total amount.
    /// </summary>
    [DataMember(Name="total_amount", EmitDefaultValue=false)]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Invoice issue date.
    /// </summary>
    [DataMember(Name="invoice_date", EmitDefaultValue=false)]
    [Required]
    public DateTimeOffset InvoiceDate { get; set; }

    /// <summary>
    /// Invoice due date.
    /// </summary>
    [DataMember(Name="invoice_due_date", EmitDefaultValue=false)]
    [Required]
    public DateTimeOffset InvoiceDueDate { get; set; }

    /// <summary>
    /// (!) Not clear why is this field for.
    /// </summary>
    [DataMember(Name="expiration_date", EmitDefaultValue=false)]
    public DateTimeOffset ExpirationDate { get; set; }

    /// <summary>
    /// Invoice notification type.
    /// </summary>

    [DataMember(Name="notificationType", EmitDefaultValue=false)]
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public InvoiceModelNotificationType NotificationType { get; set; }

    /// <summary>
    /// Invoice actual status.
    /// </summary>
    [DataMember(Name="status", EmitDefaultValue=false)]
    [Required]
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public InvoiceModelStatus Status { get; set; }

    /// <summary>
    /// Indicator whether invoice is deleted.
    /// </summary>
    [DataMember(Name="isDeleted", EmitDefaultValue=false)]
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Indicator whether invoice is approved. (!) can be an invoice not approved but paid?
    /// </summary>
    [DataMember(Name="isApproved", EmitDefaultValue=false)]
    public bool IsApproved { get; set; }

    /// <summary>
    /// Dismiss reasons when not approved.
    /// </summary>
    [DataMember(Name = "dismiss_reasons", EmitDefaultValue = false)]
    public string DismissReasons { get; set; } = null!;

    /// <summary>
    /// (!) Field purpose is not clear.
    /// </summary>
    [DataMember(Name="isSeen", EmitDefaultValue=false)]
    public bool IsSeen { get; set; }

    /// <summary>
    /// Calculated field to indicate overdue.
    /// </summary>
    [DataMember(Name="isOverdue", EmitDefaultValue=false)]
    public bool IsOverdue { get; set; }

    /// <summary>
    /// Note for invoice.
    /// </summary>
    [DataMember(Name = "note", EmitDefaultValue = false)]
    public string Note { get; set; } = null!;

    [DataMember(Name = "document", EmitDefaultValue = false)]
    public InvoiceDocumentModel Document { get; set; } = null!;
    
    /// <summary>
    /// Note for invoice.
    /// </summary>
    [DataMember(Name = "contacts", EmitDefaultValue = false)]
    public string[]? Contacts { get; set; } = null!;
}
