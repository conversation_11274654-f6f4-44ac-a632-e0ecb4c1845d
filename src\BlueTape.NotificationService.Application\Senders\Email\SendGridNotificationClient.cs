﻿using BlueTape.NotificationService.Application.Abstractions;
using BlueTape.NotificationService.Application.Senders.Email.Abstractions;
using BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;
using BlueTape.NotificationService.Domain.Constants;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Dynamic;
using System.Text.Json;

namespace BlueTape.NotificationService.Application.Senders.Email;

public class SendGridNotificationClient : ISendGridNotificationClient
{
    private readonly ISendGridClient _client;
    private readonly IConfiguration _configuration;
    private readonly ILogger<SendGridNotificationClient> _logger;
    private readonly IS3FileService _s3FileService;
    private readonly SendGridEmailOptions _options;

    public SendGridNotificationClient(
        ISendGridClient client,
        IConfiguration configuration,
        ILogger<SendGridNotificationClient> logger,
        IS3FileService s3FileService)
    {
        _client = client;
        _configuration = configuration;
        _logger = logger;
        _s3FileService = s3FileService;
        _options = new SendGridEmailOptions()
        {
            FromEmail = "<EMAIL>",
            FromName = "BlueTape Inc"
        };
    }

    public async Task<bool> SendOpsTeamNotification(NotificationChannelDocument<BlueTapeBackOfficePayloadDocument> email, CancellationToken ct)
    {
        ct.ThrowIfCancellationRequested();

        var msg = new SendGridMessage();
        msg.SetFrom(_options.FromEmail, _options.FromName);

        if (email.IsDelivered) return true;

        var receivers = GetOpsTeamEmails();
        receivers.ForEach(x =>
        {
            msg.AddTo(x, "Ops Team");
        });
        
        if (!string.IsNullOrEmpty(email.Payload.Subject))
        {
            msg.Subject = email.Payload.Subject;
        }
        else
        {
            msg.Subject = "Notification for Ops Team Notification";
        }

        if (!string.IsNullOrEmpty(email.Payload.TemplateId) && !string.IsNullOrEmpty(email.Payload.TemplatePayload))
        {
            try
            {
                var templatePayload = DeserializeToDynamicObject(email.Payload.TemplatePayload);

                msg.TemplateId = email.Payload.TemplateId;
                msg.SetTemplateData(templatePayload);
            }
            catch (Exception e)
            {

                return false;
            }
        }
        else
        {
            msg.HtmlContent = email.Payload.Html;
        }
        
        try
        {
            var response = await _client.SendEmailAsync(msg, ct);

            if (!response.IsSuccessStatusCode)
            {
                var body = await response.Body.ReadAsStringAsync(ct);
                _logger.LogError("Can't send templated email via SendGrid: Status: {StatusCode} \n Response: {body}", response.StatusCode, body);
                throw new SendGridClientException($"Can't send templated email via SendGrid: Status: {response.StatusCode} \n Response: {body}", response);
            }

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<bool> SendEmailNotification(NotificationChannelDocument<EmailPayloadDocument> email,
        CancellationToken ct)
    {
        ct.ThrowIfCancellationRequested();

        var msg = new SendGridMessage();

        if (email.Payload.From is null)
        {
            msg.SetFrom(_options.FromEmail, _options.FromName);
        }
        else
        {
            msg.SetFrom(email.Payload.From.Email, email.Payload.From.Name);
        }

        if (!string.IsNullOrEmpty(email.Payload.Subject))
        {
            msg.Subject = email.Payload.Subject;
        }
        else
        {
            msg.Subject = "Notification from BlueTape";
        }

        if(email.IsDelivered) return true;

        email.Payload.Receivers.ForEach(x =>
        {
            msg.AddTo(x.Email, x.Name);
        });

        email.Payload.CopyTo.ForEach(x =>
        {
            msg.AddCc(x.Email, x.Name);
        });

        if (!string.IsNullOrEmpty(email.Payload.TemplateId) && !string.IsNullOrEmpty(email.Payload.TemplatePayload))
        {
            try
            {
                var templatePayload = DeserializeToDynamicObject(email.Payload.TemplatePayload);
                
                msg.TemplateId = email.Payload.TemplateId;
                msg.SetTemplateData(templatePayload);
            }
            catch (Exception e)
            {

                return false;
            }
        }
        else
        {
            msg.HtmlContent = email.Payload.Html;
        }

        await AddS3AttachmentsToMessage(msg, email.Payload.S3AttachmentReferences, ct);

        try
        {
            var response = await _client.SendEmailAsync(msg, ct);

            if (!response.IsSuccessStatusCode)
            {
                var body = await response.Body.ReadAsStringAsync(ct);
                _logger.LogError("Can't send templated email via SendGrid: Status: {StatusCode} \n Response: {body}", response.StatusCode, body);
                throw new SendGridClientException($"Can't send templated email via SendGrid: Status: {response.StatusCode} \n Response: {body}", response);
            }

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public static object DeserializeToDynamicObject(string json)
    {
        if (string.IsNullOrWhiteSpace(json))
            throw new ArgumentException("Input JSON is null or empty.");

        using var document = JsonDocument.Parse(json);
        return ParseElement(document.RootElement);
    }

    private static ExpandoObject ParseElement(JsonElement element)
    {
        var expando = new ExpandoObject() as IDictionary<string, object>;

        foreach (var prop in element.EnumerateObject())
        {
            expando[prop.Name] = prop.Value.ValueKind switch
            {
                JsonValueKind.Object => ParseElement(prop.Value),
                JsonValueKind.Array => ParseArray(prop.Value),
                JsonValueKind.String => prop.Value.GetString(),
                JsonValueKind.Number => prop.Value.TryGetInt64(out var l) ? l : prop.Value.GetDouble(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => null,
                _ => prop.Value.ToString()
            };
        }

        return (ExpandoObject)expando;
    }

    private static List<object> ParseArray(JsonElement array)
    {
        var list = new List<object>();
        foreach (var item in array.EnumerateArray())
        {
            list.Add(item.ValueKind switch
            {
                JsonValueKind.Object => ParseElement(item),
                JsonValueKind.Array => ParseArray(item),
                JsonValueKind.String => item.GetString(),
                JsonValueKind.Number => item.TryGetInt64(out var l) ? l : item.GetDouble(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => null,
                _ => item.ToString()
            });
        }
        return list;
    }

    private List<string> GetOpsTeamEmails()
    {
        var email = _configuration[ConfigConstants.OperationTeamEmail];

        if (!string.IsNullOrWhiteSpace(email))
        {
            var emails = email.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(e => e.Trim()).ToList();

            return emails;
        }

        return [];
    }

    private async Task AddS3AttachmentsToMessage(SendGridMessage message, List<S3AttachmentReferenceDocument>? attachmentReferences, CancellationToken cancellationToken)
    {
        if (attachmentReferences == null || !attachmentReferences.Any())
            return;

        try
        {
            _logger.LogInformation("Processing {Count} S3 attachments", attachmentReferences.Count);

            var attachmentFiles = await _s3FileService.DownloadFilesAsync(attachmentReferences, cancellationToken);

            foreach (var (fileName, fileContent) in attachmentFiles)
            {
                var attachment = new Attachment
                {
                    Content = Convert.ToBase64String(fileContent),
                    Filename = fileName,
                    Type = GetContentType(fileName, attachmentReferences),
                    Disposition = "attachment"
                };

                message.AddAttachment(attachment);
                _logger.LogDebug("Added attachment: {FileName}, Size: {Size} bytes", fileName, fileContent.Length);
            }

            _logger.LogInformation("Successfully added {Count} attachments to email", attachmentFiles.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process S3 attachments. Email will be sent without attachments.");
            // Don't throw - send email without attachments rather than failing completely
        }
    }

    private string GetContentType(string fileName, List<S3AttachmentReferenceDocument> attachmentReferences)
    {
        var attachment = attachmentReferences.FirstOrDefault(a =>
            (a.FileName ?? Path.GetFileName(a.Key)) == fileName);

        if (!string.IsNullOrEmpty(attachment?.ContentType))
        {
            return attachment.ContentType;
        }

        // Fallback to basic content type detection based on file extension
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => "application/vnd.ms-excel",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".txt" => "text/plain",
            ".csv" => "text/csv",
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".zip" => "application/zip",
            _ => "application/octet-stream"
        };
    }
}