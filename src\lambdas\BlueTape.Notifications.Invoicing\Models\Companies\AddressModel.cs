using System.Runtime.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.Companies
{
    /// <summary>
    /// AddressModel
    /// </summary>
    [DataContract]
    public class AddressModel
    {
        /// <summary>
        /// Address line
        /// </summary>
        /// <value>Address line</value>
        [DataMember(Name="address", EmitDefaultValue=false)]
        public string Address { get; set; } = null!;

        /// <summary>
        /// Unit number
        /// </summary>
        /// <value>Unit number</value>
        [DataMember(Name="unitNumber", EmitDefaultValue=false)]
        public string UnitNumber { get; set; } = null!;

        /// <summary>
        /// City
        /// </summary>
        /// <value>City</value>
        [DataMember(Name="city", EmitDefaultValue=false)]
        public string City { get; set; } = null!;

        /// <summary>
        /// State
        /// </summary>
        /// <value>State</value>
        [DataMember(Name="state", EmitDefaultValue=false)]
        public string State { get; set; } = null!;

        /// <summary>
        /// Zipcode
        /// </summary>
        /// <value>Zipcode</value>
        [DataMember(Name="zip", EmitDefaultValue=false)]
        public string Zip { get; set; } = null!;

        /// <summary>
        /// Phone number
        /// </summary>
        /// <value>Phone number</value>
        [DataMember(Name="phone", EmitDefaultValue=false)]
        public string Phone { get; set; } = null!;
    }
}
