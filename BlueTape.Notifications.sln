﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.7.34009.444
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "lambdas", "lambdas", "{9EEC210F-2232-46AB-90E6-6A9794D23F77}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Notifications.Invoicing", "src\lambdas\BlueTape.Notifications.Invoicing\BlueTape.Notifications.Invoicing.csproj", "{26AF4725-6927-4361-8B76-FE1BC42377C7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{61199DCF-A024-4A21-89F9-1FD70CC68C0D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Notifications.Invoicing.Tests", "src\tests\BlueTape.Notifications.Invoicing.Tests\BlueTape.Notifications.Invoicing.Tests.csproj", "{3E95892F-03C0-48A8-BBD2-05B5F6E34B59}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.UnitTests.Base", "src\tests\BlueTape.UnitTests.Base\BlueTape.UnitTests.Base.csproj", "{E6AC7AA7-265F-481C-A14B-EAC466AFC238}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "nuget", "nuget", "{414AB1D7-BCDF-4ABE-A439-5832C91B632D}"
	ProjectSection(SolutionItems) = preProject
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "functions", "functions", "{D0BAFD4F-DF50-4E1A-9087-2B58B7414386}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueTape.NotificationService.API", "src\BlueTape.NotificationService.API\BlueTape.NotificationService.API.csproj", "{38586B4F-4CD0-46F6-8AE7-B0832E405735}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "library", "library", "{DA462A29-2804-4FBE-BD84-5CB9102EB1FC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueTape.NotificationService.Domain", "src\BlueTape.NotificationService.Domain\BlueTape.NotificationService.Domain.csproj", "{02C6C779-5914-4570-8F90-4692260D3D5D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueTape.Functions.NotificationJob", "src\functions\BlueTape.Functions.NotificationJob\BlueTape.Functions.NotificationJob.csproj", "{F7FA2580-E67B-4ECC-A6A4-5B0C1E93CE80}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueTape.NotificationService.Application", "src\BlueTape.NotificationService.Application\BlueTape.NotificationService.Application.csproj", "{BFBE661E-C4F3-419C-B937-DE7DD80D64D1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueTape.NotificationService.DataAccess.Mongo", "src\BlueTape.NotificationService.DataAccess.Mongo\BlueTape.NotificationService.DataAccess.Mongo.csproj", "{2E0F203B-B792-4DD8-8E14-75B7C8ECF808}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueTape.NotificationService.Application.Tests", "src\tests\BlueTape.NotificationService.Application.Tests\BlueTape.NotificationService.Application.Tests.csproj", "{8C586C6B-7D98-E511-2289-9BB2025DA1F4}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{26AF4725-6927-4361-8B76-FE1BC42377C7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{26AF4725-6927-4361-8B76-FE1BC42377C7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{26AF4725-6927-4361-8B76-FE1BC42377C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{26AF4725-6927-4361-8B76-FE1BC42377C7}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E95892F-03C0-48A8-BBD2-05B5F6E34B59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E95892F-03C0-48A8-BBD2-05B5F6E34B59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E95892F-03C0-48A8-BBD2-05B5F6E34B59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E95892F-03C0-48A8-BBD2-05B5F6E34B59}.Release|Any CPU.Build.0 = Release|Any CPU
		{E6AC7AA7-265F-481C-A14B-EAC466AFC238}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E6AC7AA7-265F-481C-A14B-EAC466AFC238}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E6AC7AA7-265F-481C-A14B-EAC466AFC238}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E6AC7AA7-265F-481C-A14B-EAC466AFC238}.Release|Any CPU.Build.0 = Release|Any CPU
		{38586B4F-4CD0-46F6-8AE7-B0832E405735}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38586B4F-4CD0-46F6-8AE7-B0832E405735}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38586B4F-4CD0-46F6-8AE7-B0832E405735}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38586B4F-4CD0-46F6-8AE7-B0832E405735}.Release|Any CPU.Build.0 = Release|Any CPU
		{02C6C779-5914-4570-8F90-4692260D3D5D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{02C6C779-5914-4570-8F90-4692260D3D5D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{02C6C779-5914-4570-8F90-4692260D3D5D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{02C6C779-5914-4570-8F90-4692260D3D5D}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7FA2580-E67B-4ECC-A6A4-5B0C1E93CE80}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7FA2580-E67B-4ECC-A6A4-5B0C1E93CE80}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7FA2580-E67B-4ECC-A6A4-5B0C1E93CE80}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7FA2580-E67B-4ECC-A6A4-5B0C1E93CE80}.Release|Any CPU.Build.0 = Release|Any CPU
		{BFBE661E-C4F3-419C-B937-DE7DD80D64D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BFBE661E-C4F3-419C-B937-DE7DD80D64D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BFBE661E-C4F3-419C-B937-DE7DD80D64D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BFBE661E-C4F3-419C-B937-DE7DD80D64D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{2E0F203B-B792-4DD8-8E14-75B7C8ECF808}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2E0F203B-B792-4DD8-8E14-75B7C8ECF808}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2E0F203B-B792-4DD8-8E14-75B7C8ECF808}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2E0F203B-B792-4DD8-8E14-75B7C8ECF808}.Release|Any CPU.Build.0 = Release|Any CPU
		{8C586C6B-7D98-E511-2289-9BB2025DA1F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8C586C6B-7D98-E511-2289-9BB2025DA1F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8C586C6B-7D98-E511-2289-9BB2025DA1F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8C586C6B-7D98-E511-2289-9BB2025DA1F4}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{26AF4725-6927-4361-8B76-FE1BC42377C7} = {9EEC210F-2232-46AB-90E6-6A9794D23F77}
		{3E95892F-03C0-48A8-BBD2-05B5F6E34B59} = {61199DCF-A024-4A21-89F9-1FD70CC68C0D}
		{E6AC7AA7-265F-481C-A14B-EAC466AFC238} = {61199DCF-A024-4A21-89F9-1FD70CC68C0D}
		{02C6C779-5914-4570-8F90-4692260D3D5D} = {DA462A29-2804-4FBE-BD84-5CB9102EB1FC}
		{F7FA2580-E67B-4ECC-A6A4-5B0C1E93CE80} = {D0BAFD4F-DF50-4E1A-9087-2B58B7414386}
		{BFBE661E-C4F3-419C-B937-DE7DD80D64D1} = {DA462A29-2804-4FBE-BD84-5CB9102EB1FC}
		{2E0F203B-B792-4DD8-8E14-75B7C8ECF808} = {DA462A29-2804-4FBE-BD84-5CB9102EB1FC}
		{8C586C6B-7D98-E511-2289-9BB2025DA1F4} = {61199DCF-A024-4A21-89F9-1FD70CC68C0D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {20F4B2B2-16B8-417A-9265-8E966CE4C9F4}
	EndGlobalSection
EndGlobal
