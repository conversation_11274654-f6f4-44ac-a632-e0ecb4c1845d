using AutoMapper;
using BlueTape.Notification.Sender.Enums;
using BlueTape.NotificationService.DataAccess.Mongo.Abstractions;
using BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;
using MongoDB.Driver;

namespace BlueTape.NotificationService.DataAccess.Mongo.Repositories;

public class SystemNotificationRepository : ISystemNotificationRepository
{
    private readonly MongoDbContext _dbContext;
    private readonly IMapper _mapper;

    public SystemNotificationRepository(
        MongoDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }
    
    public async Task<List<SystemNotificationDocument>> FetchNotificationsByCompanyAndFilters(
        string companyId,
        CancellationToken ct,
        List<string>? referenceIds = null,
        bool returnAll = true,
        NotificationSource? notificationSource = null,
        int pageNumber = 1,
        int pageSize = 10)
    {
        var filter = Builders<SystemNotificationDocument>.Filter.Where(x =>
            !string.IsNullOrEmpty(x.CompanyId) &&
            x.CompanyId.Equals(companyId));

        if(notificationSource != null && notificationSource != NotificationSource.Default)
        {
            filter = Builders<SystemNotificationDocument>.Filter.And(
                filter,
                Builders<SystemNotificationDocument>.Filter.Where(x =>
                    x.Source == notificationSource
                )
            );
        }
        
        if (referenceIds != null && referenceIds.Any())
        {
            filter = Builders<SystemNotificationDocument>.Filter.And(
                filter,
                Builders<SystemNotificationDocument>.Filter.Where(x =>
                    x.ReferenceIds != null &&
                    x.ReferenceIds.Any() &&
                    x.ReferenceIds.Any(y => referenceIds.Any(r => r.Equals(y))))
            );
        }

        var query = _dbContext.SystemNotifications.Find(filter);

        if (!returnAll)
        {
            var skip = (pageNumber - 1) * pageSize;
            query = query.Skip(skip).Limit(pageSize);
        }

        var company = await query.ToListAsync(ct);

        return _mapper.Map<List<SystemNotificationDocument>>(company);
    }

    public async Task<SystemNotificationDocument> AddAsync(SystemNotificationDocument systemNotification, CancellationToken ct)
    {
        systemNotification.CreatedAt ??= DateTime.UtcNow;
        
        if(systemNotification.EmailDelivery != null && systemNotification.EmailDelivery.Any())
        {
            foreach (var email in systemNotification.EmailDelivery)
            {
                email.DeliveryId = Guid.NewGuid().ToString();
            }
        }
        
        if(systemNotification.UserUiReviewDelivery != null && systemNotification.UserUiReviewDelivery.Any())
        {
            foreach (var userUiDelivery in systemNotification.UserUiReviewDelivery)
            {
                userUiDelivery.DeliveryId = Guid.NewGuid().ToString();
            }
        }
        
        if (systemNotification.BlueTapeBackOfficeDelivery != null && systemNotification.BlueTapeBackOfficeDelivery.Any())
        {
            foreach (var blueTapeBackOfficeDelivery in systemNotification.BlueTapeBackOfficeDelivery)
            {
                blueTapeBackOfficeDelivery.DeliveryId = Guid.NewGuid().ToString();
            }
        }
        
        await _dbContext.SystemNotifications.InsertOneAsync(systemNotification, null, ct);

        return systemNotification;
    }
    
    public async Task UpdateIsDeliveredByDeliveryIdsAsync(string notificationId, List<string> deliveryIds, CancellationToken ct)
    {
        // Fetch the document from the database
        var document = await _dbContext.SystemNotifications
            .Find(x => x.Id == notificationId)
            .FirstOrDefaultAsync(ct);

        if (document == null) return;

        var updateBuilder = Builders<SystemNotificationDocument>.Update;
        var updates = new List<UpdateDefinition<SystemNotificationDocument>>();

        // Update UserUiReviewDelivery
        if (document.UserUiReviewDelivery != null)
        {
            var updatedUserUiReviewDelivery = document.UserUiReviewDelivery
                .Select(delivery =>
                {
                    if (deliveryIds.Contains(delivery.DeliveryId))
                    {
                        delivery.IsDelivered = true;
                    }
                    return delivery;
                })
                .ToList();

            updates.Add(updateBuilder.Set(x => x.UserUiReviewDelivery, updatedUserUiReviewDelivery));
        }

        // Update EmailDelivery
        if (document.EmailDelivery != null)
        {
            var updatedEmailDelivery = document.EmailDelivery
                .Select(delivery =>
                {
                    if (deliveryIds.Contains(delivery.DeliveryId))
                    {
                        delivery.IsDelivered = true;
                    }
                    return delivery;
                })
                .ToList();

            updates.Add(updateBuilder.Set(x => x.EmailDelivery, updatedEmailDelivery));
        }
        
        // Update EmailDelivery
        if (document.BlueTapeBackOfficeDelivery != null)
        {
            var udpatedBlueTapeBackOfficeDelivery = document.BlueTapeBackOfficeDelivery
                .Select(delivery =>
                {
                    if (deliveryIds.Contains(delivery.DeliveryId))
                    {
                        delivery.IsDelivered = true;
                    }
                    return delivery;
                })
                .ToList();

            updates.Add(updateBuilder.Set(x => x.BlueTapeBackOfficeDelivery, udpatedBlueTapeBackOfficeDelivery));
        }
        
        if (!updates.Any()) return;

        updates.Add(updateBuilder.Set(x => x.UpdatedAt, DateTime.UtcNow));
        
        // Combine all updates
        var combinedUpdate = updateBuilder.Combine(updates);

        await _dbContext.SystemNotifications
            .UpdateOneAsync(x => x.Id == notificationId, combinedUpdate, cancellationToken: ct);
    }
}