﻿using System;
using System.Threading;
using System.Threading.Tasks;
using AutoFixture;
using BlueTape.AWSMessaging.Abstractions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Notifications.Invoicing.Abstractions;
using BlueTape.Notifications.Invoicing.Models;
using BlueTape.Notifications.Invoicing.Models.BankAccounts;
using BlueTape.Notifications.Invoicing.Models.Companies;
using BlueTape.Notifications.Invoicing.Models.Users;
using BlueTape.Notifications.Invoicing.Services;
using BlueTape.UnitTests.Base.Extensions;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlueTape.Notifications.Invoicing.Tests.Services.NotificationProcessorTests;

public class CheckPaymentAvailabilityAsyncTests
{
    private readonly Mock<ICompaniesClient> _companiesClient = new();
    private readonly Mock<IInvoicesClient> _invoiceClient = new();
    private readonly Mock<IAuthenticationClient> _authenticationClient = new();
    private readonly Mock<ITraceIdAccessor> _traceIdAccessor = new();
    private readonly Mock<ILogger<NotificationProcessor>> _loggerMock = new();
    private readonly Mock<ISqsEndpoint<NewInvoiceConversationRequest>> _sqs = new();
    private readonly Fixture _fixture = new();

    private NotificationProcessor GetService()
    {
        return new NotificationProcessor(
            _sqs.Object,
            _companiesClient.Object,
            _invoiceClient.Object,
            _authenticationClient.Object,
            _traceIdAccessor.Object,
            _loggerMock.Object);
    }
    
    [Fact]
    public async Task CheckPaymentAvailabilityAsync_SupplierNotAllowCard_UserHasOnlyCard_ReturnFalse()
    {
        var userCompanyId = _fixture.Create<string>();
        var supplierCompanyId = _fixture.Create<string>();
        var userBankAccountId = _fixture.Create<string>();
        var userInformationModel = new UserInformationModel()
        {
            CompanyId = userCompanyId
        };

        _companiesClient.Setup(x => x.GetCompaniesByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel[]
            {
                new() {Id = userCompanyId, BankAccounts = new []{userBankAccountId}},
                new() {Id = supplierCompanyId, Settings = new CompanySettingsModel(){ CardPricingPackageId = CompanyConstants.PricingOptOut}}
            });
        _companiesClient.Setup(x => x.GetBankAccountsByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new BankAccountModel[]
            {
                new() {Id = userBankAccountId, PaymentMethodType = CompanyConstants.CardPaymentType}
            });
        
        var service = GetService();
        var result = await service.CheckPaymentAvailabilityAsync(userInformationModel, supplierCompanyId, _fixture.Create<CancellationToken>());

        result.Should().Be(false);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Never());
    }
    
    [Fact]
    public async Task CheckPaymentAvailabilityAsync_SupplierNotAllowCard_UserHasNotAnyCards_ReturnFalse()
    {
        var userCompanyId = _fixture.Create<string>();
        var supplierCompanyId = _fixture.Create<string>();
        var userInformationModel = new UserInformationModel()
        {
            CompanyId = userCompanyId
        };

        _companiesClient.Setup(x => x.GetCompaniesByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel[]
            {
                new() {Id = userCompanyId, BankAccounts = Array.Empty<string>()},
                new() {Id = supplierCompanyId, Settings = new CompanySettingsModel(){ CardPricingPackageId = CompanyConstants.PricingOptOut}}
            });

        var service = GetService();
        var result = await service.CheckPaymentAvailabilityAsync(userInformationModel, supplierCompanyId, _fixture.Create<CancellationToken>());

        result.Should().Be(false);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Never());
    }
    
    [Fact]
    public async Task CheckPaymentAvailabilityAsync_SupplierNotAllowCard_UserHasBank_ReturnTrue()
    {
        var userCompanyId = _fixture.Create<string>();
        var supplierCompanyId = _fixture.Create<string>();
        var userBankAccountId = _fixture.Create<string>();
        var userInformationModel = new UserInformationModel()
        {
            CompanyId = userCompanyId
        };

        _companiesClient.Setup(x => x.GetCompaniesByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel[]
            {
                new() {Id = userCompanyId, BankAccounts = new []{userBankAccountId}},
                new() {Id = supplierCompanyId, Settings = new CompanySettingsModel(){ CardPricingPackageId = CompanyConstants.PricingOptOut}}
            });
        _companiesClient.Setup(x => x.GetBankAccountsByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new BankAccountModel[]
            {
                new() {Id = userBankAccountId, PaymentMethodType = CompanyConstants.BankPaymentType}
            });
        
        var service = GetService();
        var result = await service.CheckPaymentAvailabilityAsync(userInformationModel, supplierCompanyId, _fixture.Create<CancellationToken>());

        result.Should().Be(true);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Never());
    }
    
    [Fact]
    public async Task CheckPaymentAvailabilityAsync_SupplierAllowCard_UserHasCard_ReturnTrue()
    {
        var userCompanyId = _fixture.Create<string>();
        var supplierCompanyId = _fixture.Create<string>();
        var userBankAccountId = _fixture.Create<string>();
        var userInformationModel = new UserInformationModel()
        {
            CompanyId = userCompanyId
        };

        _companiesClient.Setup(x => x.GetCompaniesByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel[]
            {
                new() {Id = userCompanyId, BankAccounts = new []{userBankAccountId}},
                new() {Id = supplierCompanyId, Settings = new CompanySettingsModel(){ CardPricingPackageId = _fixture.Create<string>()}}
            });
        _companiesClient.Setup(x => x.GetBankAccountsByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new BankAccountModel[]
            {
                new() {Id = userBankAccountId, PaymentMethodType = CompanyConstants.CardPaymentType}
            });
        
        var service = GetService();
        var result = await service.CheckPaymentAvailabilityAsync(userInformationModel, supplierCompanyId, _fixture.Create<CancellationToken>());

        result.Should().Be(true);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Never());
    }
}