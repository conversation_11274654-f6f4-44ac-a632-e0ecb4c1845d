<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
        <AWSProjectType>Lambda</AWSProjectType>
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <RootNamespace>BlueTape.NotificationService.API</RootNamespace>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <LangVersion>latestmajor</LangVersion>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <None Update="appsettings.dev.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.prod.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.qa.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.beta.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.0" />
        <PackageReference Include="Azure.Identity" Version="1.10.4" />
        <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.17.1" />
        <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.5.0" />
        <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
    </ItemGroup>
    
    <ItemGroup>
        <PackageReference Include="FluentValidation" Version="11.5.1" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.2.2" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.5.1" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.5.0" />
        <PackageReference Include="NSwag.AspNetCore" Version="14.0.0" />
        <PackageReference Include="Microsoft.NETCore.Targets" Version="6.0.0-preview.4.21253.7" />
    </ItemGroup>
    
    <ItemGroup>
        <PackageReference Include="BlueTape.Swagger" Version="1.0.0" />
    </ItemGroup>
    
    <ItemGroup>
      <ProjectReference Include="..\BlueTape.NotificationService.Application\BlueTape.NotificationService.Application.csproj" />
      <ProjectReference Include="..\BlueTape.NotificationService.Domain\BlueTape.NotificationService.Domain.csproj" />
    </ItemGroup>

</Project>
