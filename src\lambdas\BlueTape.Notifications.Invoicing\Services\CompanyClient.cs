﻿using System.Diagnostics.CodeAnalysis;
using System.Text;
using System.Text.Json;
using BlueTape.Notifications.Invoicing.Abstractions;
using BlueTape.Notifications.Invoicing.Constants;
using BlueTape.Notifications.Invoicing.Extensions;
using BlueTape.Notifications.Invoicing.Models.BankAccounts;
using BlueTape.Notifications.Invoicing.Models.Companies;
using BlueTape.Notifications.Invoicing.Models.Customers;
using BlueTape.Notifications.Invoicing.Options;
using BlueTape.Services.Utilities.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.Notifications.Invoicing.Services;

[ExcludeFromCodeCoverage(Justification = "Will be covered when we will be more close to nuget. Next phase")]
public class CompaniesClient : ICompaniesClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<CompaniesClient> _logger;

    public CompaniesClient(HttpClient httpClient,
        IConfigurationService configurationService,
        IOptions<ServicesOptions> options, 
        ILogger<CompaniesClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _httpClient.BaseAddress = new Uri(options.Value.CompaniesApi);
        
        var apiKey = configurationService.GetByKey(ConfigurationKeys.CompanyApiKey).GetAwaiter().GetResult();
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"apiKey {apiKey}");
    }
    
    public async Task<CompanyModel?> GetCompanyByIdAsync(string id, CancellationToken cancellationToken)
    {
        var requestUri = $"companies/{id}";
        try
        {
            var response = await _httpClient.GetAsync(requestUri, cancellationToken);
            _logger.LogInformation("Received GetCompanyByIdAsync company API response {@response}", response);
            response.EnsureSuccessStatusCode();
            return await response.DeserializeResponse<CompanyModel>(cancellationToken);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Failed at reading data from Companies API at {baseAddress}/{requestUri}", _httpClient.BaseAddress, requestUri);
            throw;
        }
    }

    public async Task<CompanyModel[]?> GetCompaniesByIdsAsync(string[] ids, CancellationToken cancellationToken)
    {
        var requestUri = $"companies/getByIds";
        var json = JsonSerializer.Serialize(ids, new JsonSerializerOptions()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
        var httpContent = new StringContent(json, Encoding.UTF8, "application/json");
        try
        {
            var response = await _httpClient.PostAsync(requestUri, httpContent, cancellationToken);
            _logger.LogInformation("Received GetCompaniesByIdsAsync company API response {@response}", response);
            response.EnsureSuccessStatusCode();
            return await response.DeserializeResponse<CompanyModel[]>(cancellationToken);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Failed at reading data from Companies API at {baseAddress}/{requestUri}", _httpClient.BaseAddress, requestUri);
            throw;
        }
    }

    public async Task<CustomerModel?> GetCustomerByIdAsync(string id, CancellationToken cancellationToken)
    {
        var requestUri = $"customers/{id}";
        try
        {
            var response = await _httpClient.GetAsync(requestUri, cancellationToken);
            _logger.LogInformation("Received GetCustomerByIdAsync company API response {@response}", response);
            response.EnsureSuccessStatusCode();
            return await response.DeserializeResponse<CustomerModel>(cancellationToken);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Failed at reading data from Companies API at {baseAddress}/{requestUri}", _httpClient.BaseAddress, requestUri);
            throw;
        }
    }
    
    public async Task<CustomerModel[]?> GetCustomerBillingContactsAsync(string id, CancellationToken cancellationToken)
    {
        var requestUri = $"customers/billing-contacts/{id}";
        try
        {
            var response = await _httpClient.GetAsync(requestUri, cancellationToken);
            _logger.LogInformation("Received GetCustomerBillingContactsAsync company API response {@response}", response);
            response.EnsureSuccessStatusCode();
            return await response.DeserializeResponse<CustomerModel[]>(cancellationToken);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Failed at reading data from Companies API at {baseAddress}/{requestUri}", _httpClient.BaseAddress, requestUri);
            throw;
        }
    }
    
    public async Task<CustomerModel[]?> GetCustomerByIdsAsync(string[] ids, CancellationToken cancellationToken)
    {
        var requestUri = $"customers/getByIds";
        var json = JsonSerializer.Serialize(ids, new JsonSerializerOptions()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
        var httpContent = new StringContent(json, Encoding.UTF8, "application/json");
        try
        {
            var response = await _httpClient.PostAsync(requestUri, httpContent, cancellationToken);
            _logger.LogInformation("Received GetCustomerByIdsAsync company API response {@response}", response);
            response.EnsureSuccessStatusCode();
            return await response.DeserializeResponse<CustomerModel[]>(cancellationToken);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Failed at reading data from Companies API at {baseAddress}/{requestUri}", _httpClient.BaseAddress, requestUri);
            throw;
        }
    }

    public async Task<BankAccountModel?> GetBankAccountByIdAsync(string id, CancellationToken cancellationToken)
    {
        var requestUri = $"bank-account/{id}";
        try
        {
            var response = await _httpClient.GetAsync(requestUri, cancellationToken);
            _logger.LogInformation("Received GetBankAccountByIdAsync company API response {@response}", response);
            response.EnsureSuccessStatusCode();
            return await response.DeserializeResponse<BankAccountModel>(cancellationToken);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Failed at reading data from Companies API at {baseAddress}/{requestUri}", _httpClient.BaseAddress, requestUri);
            throw;
        }
    }
    
    public async Task<BankAccountModel[]?> GetBankAccountsByIdsAsync(string?[] ids, CancellationToken cancellationToken)
    {
        var requestUri = $"bank-account/getByIds";
        var json = JsonSerializer.Serialize(ids, new JsonSerializerOptions()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
        var httpContent = new StringContent(json, Encoding.UTF8, "application/json");
        try
        {
            var response = await _httpClient.PostAsync(requestUri, httpContent, cancellationToken);
            _logger.LogInformation("Received GetBankAccountsByIdsAsync company API response {@response}", response);
            response.EnsureSuccessStatusCode();
            return await response.DeserializeResponse<BankAccountModel[]>(cancellationToken);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Failed at reading data from Companies API at {baseAddress}/{requestUri}", _httpClient.BaseAddress, requestUri);
            throw;
        }
    }
}
