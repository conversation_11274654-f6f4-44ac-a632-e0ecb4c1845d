.tagging:base:
  variables:
    PROJECT_ID: "41629881"
  before_script:
    - 'export TAGDATE=$(date -u "+%Y-%m-%d_%H-%M-%SZ")'

tagging:dev:
  stage: tagging
  extends: .tagging:base
  needs:
    - job: deploying:dev
  script:
    - 'curl --request POST --header "PRIVATE-TOKEN: $TAGGING_TOKEN" "https://gitlab.com/api/v4/projects/$PROJECT_ID/repository/tags?tag_name=dev_$TAGDATE&ref=$CI_COMMIT_BRANCH"'
  only:
    !reference [ "building:api", only ]

tagging:beta:
  stage: tagging
  extends: .tagging:base
  needs:
    - job: deploying:beta
  script:
    - 'curl --request POST --header "PRIVATE-TOKEN: $TAGGING_TOKEN" "https://gitlab.com/api/v4/projects/$PROJECT_ID/repository/tags?tag_name=beta_$TAGDATE&ref=$CI_COMMIT_BRANCH"'
  only:
    !reference [ "building:api", only ]

tagging:prod:
  stage: tagging
  extends: .tagging:base
  needs:
    - job: deploying:prod
  script:
    - 'curl --request POST --header "PRIVATE-TOKEN: $TAGGING_TOKEN" "https://gitlab.com/api/v4/projects/$PROJECT_ID/repository/tags?tag_name=$TAGDATE&ref=prod"'
  only:
    - prod    