{"profiles": {"DEV: Mock Lambda Test Tool": {"commandName": "Executable", "commandLineArgs": "--port 5050", "workingDirectory": ".\\bin\\$(Configuration)\\net6.0", "executablePath": "%USERPROFILE%\\.dotnet\\tools\\dotnet-lambda-test-tool-6.0.exe", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "dev", "AWS_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************", "LOGZIO_TOKEN": "NCCCLcpNdOLBSMRoISTxozDzHoDwJjoR"}}, "BETA: Mock Lambda Test Tool": {"commandName": "Executable", "commandLineArgs": "--port 5050", "workingDirectory": ".\\bin\\$(Configuration)\\net6.0", "executablePath": "%USERPROFILE%\\.dotnet\\tools\\dotnet-lambda-test-tool-6.0.exe", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "beta", "AWS_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************", "LOGZIO_TOKEN": "NCCCLcpNdOLBSMRoISTxozDzHoDwJjoR"}}, "Mock Lambda Test Tool": {"commandName": "Executable", "commandLineArgs": "--port 5050", "workingDirectory": ".\\bin\\$(Configuration)\\net6.0", "executablePath": "%USERPROFILE%\\.dotnet\\tools\\dotnet-lambda-test-tool-6.0.exe"}, "Mock Lambda Test Tool RIDER DEV": {"commandName": "Executable", "commandLineArgs": "--port 5050", "workingDirectory": "$(ProjectDir)", "executablePath": "%USERPROFILE%\\.dotnet\\tools\\.store\\amazon.lambda.testtool-6.0\\0.12.7\\amazon.lambda.testtool-6.0\\0.12.7\\tools\\net6.0\\any\\Amazon.Lambda.TestTool.BlazorTester.dll", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "dev", "AWS_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************", "LOGZIO_TOKEN": "NCCCLcpNdOLBSMRoISTxozDzHoDwJjoR"}}}}