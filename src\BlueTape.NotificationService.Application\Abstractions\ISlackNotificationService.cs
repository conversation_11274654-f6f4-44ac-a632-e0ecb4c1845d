﻿using BlueTape.SNS.SlackNotification.Models;

namespace BlueTape.NotificationService.Application.Abstractions;

public interface ISlackNotificationService
{
    Task Notify(string message, string eventName, EventLevel eventLevel, CancellationToken ctx);
    Task Notify(EventMessageBody message, CancellationToken ctx);
    Task NotifyOpsTeam(string message, string eventName, EventLevel eventLevel, string eventSource, string traceId, CancellationToken ctx);
}