#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 443
EXPOSE 8080
EXPOSE 5198
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY . .
ARG PACKAGE_REGISTRY_USERNAME
ARG PACKAGE_REGISTRY_PASSWORD
ENV PACKAGE_REGISTRY_USERNAME=${PACKAGE_REGISTRY_USERNAME}
ENV PACKAGE_REGISTRY_PASSWORD=${PACKAGE_REGISTRY_PASSWORD}

RUN dotnet build "BlueTape.NotificationService.API/BlueTape.NotificationService.API.csproj" -c Release

FROM build AS publish
RUN dotnet publish "BlueTape.NotificationService.API/BlueTape.NotificationService.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ARG ASPNETCORE_ENVIRONMENT=Development
ENV ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT}
ENTRYPOINT ["dotnet", "BlueTape.NotificationService.API.dll"]