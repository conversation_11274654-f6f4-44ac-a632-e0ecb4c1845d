using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using BlueTape.Notifications.Invoicing.Models.Invoices.Enums;

namespace BlueTape.Notifications.Invoicing.Models.Invoices;

// ReSharper disable once ClassNeverInstantiated.Global
public class CustomerAddressModel
{
    /// <summary>
    /// (!) Review these address types.
    /// </summary>
    [DataMember(Name="addressType", EmitDefaultValue=false)]
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public CustomerAddressModelAddressType AddressType { get; set; }

    /// <summary>
    /// Address line 1.
    /// </summary>
    [DataMember(Name="addressLine1", EmitDefaultValue=false)]
    public string AddressLine1 { get; set; } = null!;

    /// <summary>
    /// Address line 2.
    /// </summary>
    [DataMember(Name="addressLine2", EmitDefaultValue=false)]
    public string AddressLine2 { get; set; } = null!;

    /// <summary>
    /// Unit number.
    /// </summary>
    [DataMember(Name="unitNumber", EmitDefaultValue=false)]
    public string UnitNumber { get; set; } = null!;

    /// <summary>
    /// City part of an address.
    /// </summary>
    [DataMember(Name="city", EmitDefaultValue=false)]
    public string City { get; set; } = null!;

    /// <summary>
    /// State part of an address.
    /// </summary>
    [DataMember(Name="state", EmitDefaultValue=false)]
    public string State { get; set; } = null!;

    /// <summary>
    /// Zipcode.
    /// </summary>
    [DataMember(Name="zip", EmitDefaultValue=false)]
    public string Zip { get; set; } = null!;

    /// <summary>
    /// Latitude part of geo data.
    /// </summary>
    [DataMember(Name="latitude", EmitDefaultValue=false)]
    public double Latitude { get; set; }

    /// <summary>
    /// Longitude part of geo data.
    /// </summary>
    [DataMember(Name="longitude", EmitDefaultValue=false)]
    public double Longitude { get; set; }
}
