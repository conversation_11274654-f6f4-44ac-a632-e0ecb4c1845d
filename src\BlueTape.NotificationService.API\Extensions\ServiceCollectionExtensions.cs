using Amazon;
using Amazon.Extensions.NETCore.Setup;
using Amazon.KeyManagementService;
using Amazon.S3;
using Amazon.SecretsManager;
using BlueTape.NotificationService.API.Helpers;
using BlueTape.Services.Utilities.AWS;
using BlueTape.Services.Utilities.Configuration;
using BlueTape.Services.Utilities.Security;

namespace BlueTape.NotificationService.API.Extensions;

internal static class ServiceCollectionExtensions
{
    internal static void AddUtilities(this IServiceCollection services)
    {
        services.AddSingleton<IMd5HashService, Md5Hashservice>();
        services.AddSingleton<ISecretsManagerService, AwsSecretsManagerService>();
        services.AddSingleton<IEncryptionService, AesEncryptionService>();
        services.AddTransient<IConfigurationService, ConfigurationService>();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddTransient<IExceptionToResponseMapper, ExceptionToResponseMapper>();
    }

    internal static void AddAWSServices(this IServiceCollection services, ConfigurationManager configuration)
    {
        var t = configuration.GetAWSOptions();
        services.AddDefaultAWSOptions(t);
        services.AddAWSService<IAmazonS3>(new AWSOptions { Region = RegionEndpoint.USWest1 });
        services.AddAWSService<IAmazonSecretsManager>(new AWSOptions { Region = RegionEndpoint.USWest1 });
        services.AddAWSService<IAmazonKeyManagementService>(new AWSOptions { Region = RegionEndpoint.USWest1 });
    }
}