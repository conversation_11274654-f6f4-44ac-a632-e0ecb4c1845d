

using System.Reflection;
using System.Text.Json.Serialization;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using BlueTape.NotificationService.API.Extensions;
using BlueTape.NotificationService.API.Handlers;
using BlueTape.NotificationService.API.Mapper;
using BlueTape.NotificationService.API.Swagger;
using BlueTape.NotificationService.Application.DI;
using BlueTape.NotificationService.Domain.Constants;
using BlueTape.NotificationService.Domain.Enrichers;
using BlueTape.Services.Utilities.AspNetCore.Tracing;
using BlueTape.Services.Utilities.Options;
using BlueTape.Utilities.Constants;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.OpenApi.Models;
using NSwag.Generation.Processors.Security;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

var configuration = builder.Configuration;
var env = builder.Environment;

if (!env.EnvironmentName.Equals(EnvironmentConstants.IntegrationTest))
{
    var keyVaultUri = new Uri(Environment.GetEnvironmentVariable(ConfigurationKeys.KeyVaultUri)!);
    var azureCredentials = new DefaultAzureCredential();
    builder.Configuration.AddAzureKeyVault(keyVaultUri, azureCredentials, new AzureKeyVaultConfigurationOptions
    {
        ReloadInterval = TimeSpan.FromMinutes(EnvironmentConstants.MinutestKeyVaultReload)
    });
}

var appInsightsConnectionString = configuration.GetSection("APP-INSIGHTS-CONNECTION").Value;

if (!env.EnvironmentName.Equals(EnvironmentConstants.Local))
{
    builder.Services.AddApplicationInsightsTelemetry(cfg =>
    {
        cfg.ConnectionString = appInsightsConnectionString;
    });
}

builder.Host.UseSerilog((hostingContext, loggerConfiguration) =>
{
    loggerConfiguration
        .ReadFrom.Configuration(configuration)
        .Enrich.FromGlobalLogContext()
        .Enrich.WithProperty("ProjectName", ConfigurationKeys.ProjectValue)
        .Enrich.WithProperty("EnvironmentName", hostingContext.HostingEnvironment.EnvironmentName)
        .Enrich.WithProperty("ContentRootPath", hostingContext.HostingEnvironment.ContentRootPath)
        .Enrich.With<StackTraceEnricher>()
        .WriteTo.ApplicationInsights(appInsightsConnectionString, TelemetryConverter.Traces);

    loggerConfiguration.WriteTo.Console();

    if (hostingContext.HostingEnvironment.IsDevelopment())
        loggerConfiguration.WriteTo.Console();
});

builder.Services.AddControllers()
    .AddJsonOptions(opts =>
    {
        var enumConverter = new JsonStringEnumConverter();
        opts.JsonSerializerOptions.Converters.Add(enumConverter);
    });

builder.Services.AddAuthentication(o =>
    {
        o.DefaultScheme = ConfigurationKeys.ApiKeyAuthScheme;
        o.DefaultChallengeScheme = ConfigurationKeys.ApiKeyAuthScheme;
    })
    .AddScheme<AuthenticationSchemeOptions, ApiKeyHandler>(ConfigurationKeys.ApiKeyAuthScheme, o => { });

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowOrigins", builder =>
    {
        /*
        builder.WithOrigins("http://localhost:3000", "https://localhost:3000",
                "https://bluetape.com",
                "https://beta.bluetape.com",
                "https://beta-api.bluetape.com",
                "https://dev.bluetape.com",
                "https://app.bluetape.com",
                "https://api.bluetape.com"
            )
            .AllowAnyHeader().AllowAnyMethod();*/

        builder.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod();
    });
});


builder.Services.AddDefaultAWSOptions(configuration.GetAWSOptions());
builder.Services.AddOptions();
builder.Services.Configure<BlueTapeOptions>(builder.Configuration.GetSection(nameof(BlueTapeOptions)));

builder.Services.AddHttpLogging(c =>
{
    c.LoggingFields = HttpLoggingFields.All;
});
builder.Services.AddBlueTapeTracing();
builder.Services.AddAWSServices(builder.Configuration);
builder.Services.AddUtilities();
builder.Services.AddApplicationDependencies(builder.Configuration);

builder.Services.AddAutoMapper(typeof(APIProfile).GetTypeInfo().Assembly);

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
    {
        var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
        c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));

        c.AddSecurityDefinition(ConfigurationKeys.ApiKeyAuthScheme,
            new OpenApiSecurityScheme
            {
                Description = "API key used in the Authorization header.",
                Name = ConfigurationKeys.ApiKeyName,
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey
            });

        c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = ConfigurationKeys.ApiKeyAuthScheme
                    }
                },
                new List<string>()
            }
        });

        c.OperationFilter<TraceIdHeaderParameterFilter>();
    }
);

builder.Services.AddOpenApiDocument(c =>
{
    c.Title = "BlueTape Notification Service API";
    c.OperationProcessors.Add(new AspNetCoreOperationSecurityScopeProcessor("ApiKey"));
});

builder.Services.AddHealthChecks();

var app = builder.Build();
app.UseBlueTapeTracing();

var forwardedHeaderOptions = new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.All
};
forwardedHeaderOptions.KnownNetworks.Clear();
forwardedHeaderOptions.KnownProxies.Clear();

app.UseForwardedHeaders(forwardedHeaderOptions);

app.UseHttpLogging();

app.UseHttpsRedirection();

app.UseCors("AllowOrigins");

if (!env.IsEnvironment(EnvironmentConstants.Production))
{
    app.UseSwagger(opt => opt.PreSerializeFilters.Add((swagger, httpReq) =>
    {
        if (!httpReq.Host.Host.Contains(RouteConstants.LocalHost) && !env.IsEnvironment(EnvironmentConstants.Dev))
        {
            swagger.Servers = new List<OpenApiServer> { new() { Url = RouteConstants.Suffix } };
        }
    }));
    app.UseSwaggerUI();
}

app.IncludeMiddlewares();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.MapHealthChecks("/health").AllowAnonymous();

app.Run();

Log.CloseAndFlush();