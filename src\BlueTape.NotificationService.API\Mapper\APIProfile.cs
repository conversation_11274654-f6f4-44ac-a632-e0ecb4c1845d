using AutoMapper;
using BlueTape.Notification.Sender.APIModels;
using BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;

namespace BlueTape.NotificationService.API.Mapper;

public class APIProfile : Profile
{
    public APIProfile()
    {
        CreateMap<SystemNotificationDocument, SystemNotificationModel>();
        CreateMap<UserUiReviewPayloadDocument, UserUiReviewPayloadModel>();
        
        CreateMap<EmailReceiverData, EmailReceiverDataModel>();
        CreateMap<NotificationChannelDocument<UserUiReviewPayloadDocument>, NotificationChannelModel<UserUiReviewPayloadModel>>();

        CreateMap<EmailPayloadDocument, EmailPayloadModel>();
        CreateMap<NotificationChannelDocument<EmailPayloadDocument>, NotificationChannelModel<EmailPayloadModel>>();

        CreateMap<BlueTapeBackOfficePayloadDocument, BlueTapeBackOfficePayloadModel>();
        CreateMap<NotificationChannelDocument<BlueTapeBackOfficePayloadDocument>, NotificationChannelModel<BlueTapeBackOfficePayloadModel>>();

        CreateMap<SlackPayloadDocument, SlackPayloadModel>();

    }
}