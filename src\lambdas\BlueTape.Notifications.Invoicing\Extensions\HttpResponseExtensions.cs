﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace BlueTape.Notifications.Invoicing.Extensions;

public static class HttpResponseExtensions
{
    public static async Task<T?> DeserializeResponse<T>(this HttpResponseMessage response, CancellationToken cancellationToken)
    {
        var jsonString = await response.Content.ReadAsStringAsync(cancellationToken);
        return string.IsNullOrWhiteSpace(jsonString)
            ? default
            : JsonSerializer.Deserialize<T>(jsonString, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                Converters = { new JsonStringEnumConverter() }
            });
    }
}