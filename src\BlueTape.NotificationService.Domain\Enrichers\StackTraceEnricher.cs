﻿using System.Diagnostics;
using Serilog.Core;
using Serilog.Events;

namespace BlueTape.NotificationService.Domain.Enrichers;

public class StackTraceEnricher : ILogEventEnricher
{
    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        if (logEvent.Level >= LogEventLevel.Verbose)
        {
            var stackTrace = new StackTrace(true).ToString();
            var stackTraceProperty = propertyFactory.CreateProperty("StackTrace", stackTrace);
            logEvent.AddPropertyIfAbsent(stackTraceProperty);
        }
    }
}