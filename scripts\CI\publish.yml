.publishing:base:
   stage: publish 
   variables:
     GIT_DEPTH: "0"
   script: 
    - apt-get update
    - apt-get -y install zip
    - dotnet tool restore
    - dotnet lambda package -pl src/lambdas/BlueTape.Notifications.Invoicing/ -o publish/bridge/bridge-$CI_PIPELINE_ID.zip

   artifacts:
    name: $CI_JOB_NAME
    paths:
      - publish/bridge/bridge-$CI_PIPELINE_ID.zip
    expire_in: 1 week 

publishing:api:
  extends: .publishing:base
  only:
   !reference [ "building:api", only ]
  when: manual

publishing:prod:
  extends: .publishing:base
  only:
   !reference [ "building:prod", only ]
  when: manual