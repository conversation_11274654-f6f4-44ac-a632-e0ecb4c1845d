﻿using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.NotificationService.Application.Abstractions;
using BlueTape.NotificationService.Domain.Options;
using BlueTape.SNS.SlackNotification;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Constants;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Globalization;

namespace BlueTape.NotificationService.Application.Services;

public class SlackNotificationService(
    ILogger<SlackNotificationService> logger,
    ISnsEndpoint snsEndpoint,
    ITraceIdAccessor traceIdAccessor,
    IOptions<SlackNotificationOptions> options)
    : ISlackNotificationService
{
    private readonly SlackNotificationOptions _slackNotificationOptions = options.Value;
    private const string ServiceName = "NotificaionService";

    public Task Notify(string message, string eventName, EventLevel eventLevel, CancellationToken ctx)
    {
        return Notify(new EventMessageBody
        {
            Message = message,
            EventLevel = eventLevel,
            EventName = eventName,
            EventSource = EnvironmentExtensions.GetExecutionEnvironment(),
            ServiceName = ServiceName,
            TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
            AwsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                           "Not provided in service",
        }, ctx);
    }

    public async Task Notify(EventMessageBody message, CancellationToken ctx)
    {
        var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
        if (env is (EnvironmentConstants.Development or EnvironmentConstants.Local))
            return;

        var correlationId = traceIdAccessor.TraceId;
        message.Message += $"\nBlueTapeCorrelationId: {correlationId}";
        var awsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                           "Not provided in service";
        var region = Environment.GetEnvironmentVariable("AWS_REGION");

        if (string.IsNullOrEmpty(_slackNotificationOptions.ErrorSnsTopicName)) return;
        var topic = $"arn:aws:sns:{region}:{awsAccountId}:{_slackNotificationOptions.ErrorSnsTopicName}";

        await snsEndpoint.PublishSlackNotificationAsync(
            message,
            topic,
            "Notification service event triggered",
            ctx);
    }

    public async Task NotifyOpsTeam(string message, string eventName, EventLevel eventLevel, string eventSource, string traceId, CancellationToken ctx)
    {
        var awsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                           "Not provided in service";
        var region = Environment.GetEnvironmentVariable("AWS_REGION");

        if (string.IsNullOrEmpty(_slackNotificationOptions.OpsTeamTopicName)) return;

        var topic = $"arn:aws:sns:{region}:{awsAccountId}:{_slackNotificationOptions.OpsTeamTopicName}";

        await Notify(new EventMessageBody
        {
            Message = message,
            EventLevel = eventLevel,
            EventName = eventName,
            EventSource = eventSource,
            ServiceName = "NotificationService",
            TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
            AwsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                           "Not provided in service",
        }, topic, "Ops team notfication", traceId, ctx);
    }

    private async Task Notify(EventMessageBody message, string topic, string subject, string traceId, CancellationToken ctx)
    {
        var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
        if (env is (EnvironmentConstants.Development or EnvironmentConstants.Local))
            return;

        message.Message += $"\nBlueTapeCorrelationId: {traceId}";

        await snsEndpoint.PublishSlackNotificationAsync(
            message,
            topic,
            subject,
            ctx);
    }
}