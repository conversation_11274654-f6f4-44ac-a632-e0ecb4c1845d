﻿using System.Net;
using System.Text.Json;
using System.Text.Json.Serialization;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.NotificationService.API.Helpers;
using BlueTape.NotificationService.Application.Abstractions;
using BlueTape.NotificationService.Domain.Constants;
using BlueTape.SNS.SlackNotification.Models;

namespace BlueTape.NotificationService.API.Middlewares;

public class ExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IExceptionToResponseMapper _exceptionToResponseMapper;
    private readonly ILogger<ExceptionMiddleware> _logger;
    private readonly ISlackNotificationService _notificationService;

    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        Converters = { new JsonStringEnumConverter() },
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    };

    public ExceptionMiddleware(
        RequestDelegate next,
        IExceptionToResponseMapper exceptionToResponseMapper,
        ILogger<ExceptionMiddleware> logger,
        ISlackNotificationService notificationService)
    {
        _next = next;
        _exceptionToResponseMapper = exceptionToResponseMapper;
        _logger = logger;
        _notificationService = notificationService;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next.Invoke(context);
        }
        catch (Exception exception)
        {
            _logger.LogError(exception, "{Message}", exception.Message);
            await HandleExceptionAsync(context, exception);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var (exceptionResponse, statusCode) = _exceptionToResponseMapper.Map(exception);

        var error = JsonSerializer.Serialize(exceptionResponse);

        _logger.LogError("{error}", error);
        _logger.LogError("Error query: {query}", context.Request.Path);
        _logger.LogError("{stackTrace}", exception.StackTrace);

        if (exception is DomainException domainException)
        {
            await _notificationService.Notify(domainException.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), ConfigurationKeys.ProjectValue, EventLevel.Error), CancellationToken.None);
        }
        else
        {
            await _notificationService.Notify(exception.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), ConfigurationKeys.ProjectValue, EventLevel.Error), CancellationToken.None);
        }

        if (context.Response.StatusCode == (int)HttpStatusCode.InternalServerError)
        {
            await _notificationService.Notify($"Internal Error was happened during API request: ${context.Request.Path}. Message: {exception.Message}",
                "API Internal Error",
                EventLevel.Error,
                CancellationToken.None);
        }

        context.Response.StatusCode = (int)statusCode;

        if (exceptionResponse.Count == 0)
        {
            await context.Response.WriteAsync(string.Empty);
            return;
        }

        context.Response.ContentType = "application/json";
        await JsonSerializer.SerializeAsync(context.Response.Body, exceptionResponse, JsonSerializerOptions);
    }
}
