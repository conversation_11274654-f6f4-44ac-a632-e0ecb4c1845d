﻿using System.Text.Json.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.BankAccounts;

public class BillingAddressModel
{
    [JsonPropertyName("addressLine1")]
    public string? AddressLine1 { get; set; }
    
    [JsonPropertyName("addressLine2")]
    public string? AddressLine2 { get; set; }
    
    [JsonPropertyName("city")]
    public string? City { get; set; }
    
    [JsonPropertyName("stateCode")]
    public string? StateCode { get; set; }
    
    [JsonPropertyName("zipCode")]
    public string? ZipCode { get; set; }
}