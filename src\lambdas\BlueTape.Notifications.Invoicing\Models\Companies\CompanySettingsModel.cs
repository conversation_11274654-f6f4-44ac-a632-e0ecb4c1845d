using System.Text.Json.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.Companies;

public class CompanySettingsModel
{
    [JsonPropertyName("cardPricingPackageId")]
    public string? CardPricingPackageId { get; set; }
    
    [JsonPropertyName("loanPricingPackageId")]
    public string? LoanPricingPackageId { get; set; }
    
    public bool SupportCardPayments()
    {
        return !string.IsNullOrEmpty(CardPricingPackageId) &&
               !CardPricingPackageId.Equals(CompanyConstants.PricingOptOut);
    }
}