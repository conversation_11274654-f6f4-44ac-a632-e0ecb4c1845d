﻿using BlueTape.NotificationService.Domain.Constants;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace BlueTape.NotificationService.API.Swagger;

public class TraceIdHeaderParameterFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        operation.Parameters ??= new List<OpenApiParameter>();

        operation.Parameters.Add(new OpenApiParameter()
        {
            Name = ConfigurationKeys.TraceIdHeaderName,
            In = ParameterLocation.Header,
            Required = false
        });
    }
}
