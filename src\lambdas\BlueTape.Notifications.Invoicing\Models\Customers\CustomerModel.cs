﻿using System.Text.Json.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.Customers;

public class CustomerModel
{
    [JsonPropertyName("id")]
    public string BlueTapeCustomerId { get; set; } = null!;
    
    [JsonPropertyName("companyId")]
    public string CompanyId { get; set; } = null!;
    
    [JsonPropertyName("displayName")]
    public string DisplayName { get; set; } = null!;
    
    [JsonPropertyName("firstName")]
    public string FirstName { get; set; } = null!;
    
    [JsonPropertyName("lastName")]
    public string LastName { get; set; } = null!;
    
    [JsonPropertyName("emailAddress")]
    public string? EmailAddress { get; set; } = null!;
    
    [JsonPropertyName("cellPhoneNumber")]
    public string? CellPhoneNumber { get; set; } = null!;
    
    [JsonPropertyName("businessPhoneNumber")]
    public string? BusinessPhoneNumber { get; set; }
    
    [JsonPropertyName("businessAddress")]
    public string? BusinessAddress { get; set;}
    
    [JsonPropertyName("integration")]
    public CustomerConnectorModel? Integration { get; set; }
    
    [JsonPropertyName("isDraft")]
    public bool IsDraft { get; set; }
    
    [JsonPropertyName("isDeleted")]
    public bool IsDeleted { get; set; }
    
    [JsonPropertyName("contactSource")]
    public ContactSource? ContactSource { get; set; }
    
    [JsonPropertyName("syncToken")]
    public string? SyncToken { get; set; }
}