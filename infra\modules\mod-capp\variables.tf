variable "environment" {
  type    = string
}

variable "resource_group_id" {
  type    = string
}

variable "resource_group_name" {
  type    = string
}

variable "application_name" {
  type = map(any)
}

variable "container_registry_name" {
  type    = string
}

variable "image_version" {
  type    = string
}

variable "acr_password" {
  description = "ACR password"
}

variable "client_id" {
  description = "ARM client id"
}

variable "tenant_id" {
  description = "ARM Tenant id"
}

variable "client_secret" {
  description = "ARM client secret"
}

variable "lp_aws_account" {
  description = "AWS account id"
  default = ""
}

variable "key_vault_uri" {
  type    = string
  description = "KeyVault URI"
}

variable "aws_access_key_id" {
  description = "AWS access key id"
}

variable "aws_secret_access_key" {
  description = "AWS secret access key"
}

variable "aws_default_region" {
  description = "AWS default region"
  default = "us-west-1"
}

variable "container_app_environment_id" {
  type    = string
  description = "container app environment id"
}

variable "container_registry_login_server" {
  type    = string
  description = "container registry login server"
}

variable "container_registry_admin_username" {
  type    = string
  description = "container registry admin username"
}
