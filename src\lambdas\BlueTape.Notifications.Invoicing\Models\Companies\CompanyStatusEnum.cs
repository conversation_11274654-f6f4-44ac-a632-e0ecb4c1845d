﻿using System.Runtime.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.Companies;

/// <summary>
/// Defines Status
/// </summary>
public enum CompanyStatus
{
    /// <summary>
    /// Enum New for value: new
    /// </summary>
    [EnumMember(Value = "new")]
    New = 1,
    /// <summary>
    /// Enum Applied for value: applied
    /// </summary>
    [EnumMember(Value = "applied")]
    Applied = 2,
    /// <summary>
    /// Enum Approved for value: approved
    /// </summary>
    [EnumMember(Value = "approved")]
    Approved = 3        
}