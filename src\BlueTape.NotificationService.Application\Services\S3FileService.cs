using Amazon.S3;
using Amazon.S3.Model;
using BlueTape.NotificationService.Application.Abstractions;
using BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;
using Microsoft.Extensions.Logging;

namespace BlueTape.NotificationService.Application.Services;

public class S3FileService : IS3FileService
{
    private readonly IAmazonS3 _s3Client;
    private readonly ILogger<S3FileService> _logger;

    public S3FileService(IAmazonS3 s3Client, ILogger<S3FileService> logger)
    {
        _s3Client = s3Client;
        _logger = logger;
    }

    public async Task<byte[]> DownloadFileAsync(string bucketName, string key, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Downloading file from S3: Bucket={BucketName}, Key={Key}", bucketName, key);

            var request = new GetObjectRequest
            {
                BucketName = bucketName,
                Key = key
            };

            using var response = await _s3Client.GetObjectAsync(request, cancellationToken);
            using var memoryStream = new MemoryStream();
            await response.ResponseStream.CopyToAsync(memoryStream, cancellationToken);

            var fileContent = memoryStream.ToArray();
            _logger.LogInformation("Successfully downloaded file from S3: Bucket={BucketName}, Key={Key}, Size={Size} bytes",
                bucketName, key, fileContent.Length);

            return fileContent;
        }
        catch (AmazonS3Exception ex)
        {
            _logger.LogError(ex, "Failed to download file from S3: Bucket={BucketName}, Key={Key}, ErrorCode={ErrorCode}",
                bucketName, key, ex.ErrorCode);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error downloading file from S3: Bucket={BucketName}, Key={Key}",
                bucketName, key);
            throw;
        }
    }

    public async Task<Dictionary<string, byte[]>> DownloadFilesAsync(List<S3AttachmentReferenceDocument> attachmentReferences, CancellationToken cancellationToken)
    {
        var result = new Dictionary<string, byte[]>();

        if (attachmentReferences == null || !attachmentReferences.Any())
        {
            _logger.LogInformation("No attachment references provided");
            return result;
        }

        _logger.LogInformation("Downloading {Count} files from S3", attachmentReferences.Count);

        var downloadTasks = attachmentReferences.Select(async attachment =>
        {
            try
            {
                var fileContent = await DownloadFileAsync(attachment.BucketName, attachment.Key, cancellationToken);
                var fileName = attachment.FileName ?? Path.GetFileName(attachment.Key);

                return new { FileName = fileName, Content = fileContent, Success = true };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to download attachment: Bucket={BucketName}, Key={Key}",
                    attachment.BucketName, attachment.Key);
                return new { FileName = string.Empty, Content = Array.Empty<byte>(), Success = false };
            }
        });

        var downloadResults = await Task.WhenAll(downloadTasks);

        foreach (var downloadResult in downloadResults.Where(r => r.Success))
        {
            result[downloadResult.FileName] = downloadResult.Content;
        }

        _logger.LogInformation("Successfully downloaded {SuccessCount} out of {TotalCount} attachments",
            result.Count, attachmentReferences.Count);

        return result;
    }

    public async Task<bool> FileExistsAsync(string bucketName, string key, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Checking if file exists in S3: Bucket={BucketName}, Key={Key}", bucketName, key);

            var request = new GetObjectMetadataRequest
            {
                BucketName = bucketName,
                Key = key
            };

            await _s3Client.GetObjectMetadataAsync(request, cancellationToken);

            _logger.LogDebug("File exists in S3: Bucket={BucketName}, Key={Key}", bucketName, key);
            return true;
        }
        catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            _logger.LogDebug("File does not exist in S3: Bucket={BucketName}, Key={Key}", bucketName, key);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if file exists in S3: Bucket={BucketName}, Key={Key}",
                bucketName, key);
            throw;
        }
    }
}
