﻿using System;
using System.Threading;
using System.Threading.Tasks;
using AutoFixture;
using BlueTape.AWSMessaging.Abstractions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Notifications.Invoicing.Abstractions;
using BlueTape.Notifications.Invoicing.Models;
using BlueTape.Notifications.Invoicing.Models.BankAccounts;
using BlueTape.Notifications.Invoicing.Models.Companies;
using BlueTape.Notifications.Invoicing.Models.Customers;
using BlueTape.Notifications.Invoicing.Models.Invoices;
using BlueTape.Notifications.Invoicing.Models.Users;
using BlueTape.Notifications.Invoicing.Services;
using BlueTape.UnitTests.Base.Extensions;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlueTape.Notifications.Invoicing.Tests.Services.NotificationProcessorTests;

public class ProcessAsyncTests
{
    private readonly Mock<ICompaniesClient> _companiesClient = new();
    private readonly Mock<IInvoicesClient> _invoiceClient = new();
    private readonly Mock<IAuthenticationClient> _authenticationClient = new();
    private readonly Mock<ITraceIdAccessor> _traceIdAccessor = new();
    private readonly Mock<ILogger<NotificationProcessor>> _loggerMock = new();
    private readonly Mock<ISqsEndpoint<NewInvoiceConversationRequest>> _sqs = new();
    private readonly Fixture _fixture = new();

    private NotificationProcessor GetService()
    {
        return new NotificationProcessor(
            _sqs.Object,
            _companiesClient.Object,
            _invoiceClient.Object,
            _authenticationClient.Object,
            _traceIdAccessor.Object,
            _loggerMock.Object);
    }
    
    [Fact]
    public async Task ProcessAsync_UserNot_Registered_CustomerPhoneExist()
    {
        var invoiceRequest = new NewInvoiceRequest()
        {
            InvoiceId = Guid.NewGuid().ToString()
        };
        var customerAccountId = Guid.NewGuid().ToString();
        var email = _fixture.Create<string>(); 
        var phone = _fixture.Create<string>(); 
        var traceIdResponse = _fixture.Create<string>(); 

        _invoiceClient.Setup(x => x.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new InvoiceModel()
            {
                CustomerAccountId = customerAccountId
            });
        _companiesClient.Setup(x => x.GetCustomerByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CustomerModel()
            {
                CellPhoneNumber = phone,
                EmailAddress = email
            });
        _authenticationClient.Setup(x => x.GetUserInfoByPhoneOrEmailAsync(It.IsAny<string>(), It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync((UserInformationModel) default!);
        _traceIdAccessor.SetupGet(x => x.TraceId).Returns(traceIdResponse);

        var requestCallBack = new NewInvoiceConversationRequest();
        var traceIdCallBack = string.Empty;
        _sqs.Setup(x => x.SendAsync(It.IsAny<NewInvoiceConversationRequest>(),
                It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Callback<NewInvoiceConversationRequest, string, CancellationToken>((e, a, c) =>
            {
                requestCallBack = e;
                traceIdCallBack = a;
            });
        
        var service = GetService();
        await service.ProcessAsync(invoiceRequest);

        requestCallBack.Phone.Should().BeEmpty();
        requestCallBack.DefaultNotificationPhoneList.Count.Should().Be(1);
        requestCallBack.DefaultNotificationPhoneList.Should().Contain(phone);
        traceIdCallBack.Should().BeEquivalentTo(traceIdResponse);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());
    }
    
    [Fact]
    public async Task ProcessAsync_UserNot_Registered_CustomerPhoneNotExist()
    {
        var invoiceRequest = new NewInvoiceRequest()
        {
            InvoiceId = Guid.NewGuid().ToString()
        };
        var customerAccountId = Guid.NewGuid().ToString();
        var email = _fixture.Create<string>(); 
        var phone = string.Empty; 
        var traceIdResponse = _fixture.Create<string>(); 

        _invoiceClient.Setup(x => x.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new InvoiceModel()
            {
                CustomerAccountId = customerAccountId
            });
        _companiesClient.Setup(x => x.GetCustomerByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CustomerModel()
            {
                CellPhoneNumber = phone,
                EmailAddress = email
            });
        _authenticationClient.Setup(x => x.GetUserInfoByPhoneOrEmailAsync(It.IsAny<string>(), It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync((UserInformationModel) default!);
        _traceIdAccessor.SetupGet(x => x.TraceId).Returns(traceIdResponse);

        var service = GetService();
        await service.ProcessAsync(invoiceRequest);

        _sqs.Verify(x => x.SendAsync(It.IsAny<NewInvoiceConversationRequest>(),
            It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());
    }
    
    [Fact]
    public async Task ProcessAsync_User_RegisteredWithoutPhone()
    {
        var invoiceRequest = new NewInvoiceRequest()
        {
            InvoiceId = Guid.NewGuid().ToString()
        };
        var customerAccountId = Guid.NewGuid().ToString();
        var email = _fixture.Create<string>(); 
        var phone = _fixture.Create<string>(); 
        var traceIdResponse = _fixture.Create<string>(); 

        _invoiceClient.Setup(x => x.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new InvoiceModel()
            {
                CustomerAccountId = customerAccountId
            });
        _companiesClient.Setup(x => x.GetCustomerByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CustomerModel()
            {
                CellPhoneNumber = phone,
                EmailAddress = email
            });
        _authenticationClient.Setup(x => x.GetUserInfoByPhoneOrEmailAsync(It.IsAny<string>(), It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new UserInformationModel());
        _traceIdAccessor.SetupGet(x => x.TraceId).Returns(traceIdResponse);

        var requestCallBack = new NewInvoiceConversationRequest();
        var traceIdCallBack = string.Empty;
        _sqs.Setup(x => x.SendAsync(It.IsAny<NewInvoiceConversationRequest>(),
                It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Callback<NewInvoiceConversationRequest, string, CancellationToken>((e, a, c) =>
            {
                requestCallBack = e;
                traceIdCallBack = a;
            });
        
        var service = GetService();
        await service.ProcessAsync(invoiceRequest);

        requestCallBack.Phone.Should().BeEmpty();
        requestCallBack.DefaultNotificationPhoneList.Count.Should().Be(1);
        requestCallBack.DefaultNotificationPhoneList.Should().Contain(phone);
        traceIdCallBack.Should().BeEquivalentTo(traceIdResponse);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Once());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Never());
    }
    
    [Fact]
    public async Task ProcessAsync_User_RegisteredWithoutPhone_CustomerWithoutPhone()
    {
        var invoiceRequest = new NewInvoiceRequest()
        {
            InvoiceId = Guid.NewGuid().ToString()
        };
        var customerAccountId = Guid.NewGuid().ToString();
        var email = _fixture.Create<string>(); 
        var phone = string.Empty; 
        var traceIdResponse = _fixture.Create<string>(); 

        _invoiceClient.Setup(x => x.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new InvoiceModel()
            {
                CustomerAccountId = customerAccountId
            });
        _companiesClient.Setup(x => x.GetCustomerByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CustomerModel()
            {
                CellPhoneNumber = phone,
                EmailAddress = email
            });
        _authenticationClient.Setup(x => x.GetUserInfoByPhoneOrEmailAsync(It.IsAny<string>(), It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new UserInformationModel());
        _traceIdAccessor.SetupGet(x => x.TraceId).Returns(traceIdResponse);

        var requestCallBack = new NewInvoiceConversationRequest();
        var traceIdCallBack = string.Empty;
        _sqs.Setup(x => x.SendAsync(It.IsAny<NewInvoiceConversationRequest>(),
                It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Callback<NewInvoiceConversationRequest, string, CancellationToken>((e, a, c) =>
            {
                requestCallBack = e;
                traceIdCallBack = a;
            });
        
        var service = GetService();
        await service.ProcessAsync(invoiceRequest);
        
        _sqs.Verify(x => x.SendAsync(It.IsAny<NewInvoiceConversationRequest>(),
            It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Once());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Never());
    }
    
    [Fact]
    public async Task ProcessAsync_User_RegisteredWithPhone_UserCanPay()
    {
        var invoiceRequest = new NewInvoiceRequest()
        {
            InvoiceId = Guid.NewGuid().ToString()
        };
        var customerAccountId = Guid.NewGuid().ToString();
        var email = _fixture.Create<string>(); 
        var phone = _fixture.Create<string>(); 
        var traceIdResponse = _fixture.Create<string>(); 
        var userCompanyId = _fixture.Create<string>();
        var supplierCompanyId = _fixture.Create<string>();
        var userBankAccountId = _fixture.Create<string>();

        _invoiceClient.Setup(x => x.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new InvoiceModel()
            {
                CompanyId = supplierCompanyId,
                CustomerAccountId = customerAccountId
            });
        _companiesClient.Setup(x => x.GetCustomerByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CustomerModel()
            {
                CellPhoneNumber = phone,
                EmailAddress = email
            });
        _authenticationClient.Setup(x => x.GetUserInfoByPhoneOrEmailAsync(It.IsAny<string>(), It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new UserInformationModel()
            {
                CompanyId = userCompanyId,
                Phone = phone
            });
        
        _traceIdAccessor.SetupGet(x => x.TraceId).Returns(traceIdResponse);

        var requestCallBack = new NewInvoiceConversationRequest();
        var traceIdCallBack = string.Empty;
        _sqs.Setup(x => x.SendAsync(It.IsAny<NewInvoiceConversationRequest>(),
                It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Callback<NewInvoiceConversationRequest, string, CancellationToken>((e, a, c) =>
            {
                requestCallBack = e;
                traceIdCallBack = a;
            });
        _companiesClient.Setup(x => x.GetCompaniesByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel[]
            {
                new() {Id = userCompanyId, BankAccounts = new []{userBankAccountId}},
                new() {Id = supplierCompanyId, Settings = new CompanySettingsModel(){ CardPricingPackageId = _fixture.Create<string>()}}
            });
        _companiesClient.Setup(x => x.GetBankAccountsByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new BankAccountModel[]
            {
                new() {Id = userBankAccountId, PaymentMethodType = CompanyConstants.CardPaymentType}
            });
        
        var service = GetService();
        await service.ProcessAsync(invoiceRequest);
        
        requestCallBack.Phone.Should().BeEquivalentTo(phone);
        requestCallBack.DefaultNotificationPhoneList.Count.Should().Be(0);
        traceIdCallBack.Should().BeEquivalentTo(traceIdResponse);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());
        _sqs.Verify(x => x.SendAsync(It.IsAny<NewInvoiceConversationRequest>(),
            It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once());
    }
    
    [Fact]
    public async Task ProcessAsync_User_RegisteredWithPhone_UserCanNotPay()
    {
        var invoiceRequest = new NewInvoiceRequest()
        {
            InvoiceId = Guid.NewGuid().ToString()
        };
        var customerAccountId = Guid.NewGuid().ToString();
        var email = _fixture.Create<string>(); 
        var phone = _fixture.Create<string>(); 
        var traceIdResponse = _fixture.Create<string>(); 
        var userCompanyId = _fixture.Create<string>();
        var supplierCompanyId = _fixture.Create<string>();
        var userBankAccountId = _fixture.Create<string>();

        _invoiceClient.Setup(x => x.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new InvoiceModel()
            {
                CompanyId = supplierCompanyId,
                CustomerAccountId = customerAccountId
            });
        _companiesClient.Setup(x => x.GetCustomerByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CustomerModel()
            {
                CellPhoneNumber = phone,
                EmailAddress = email
            });
        _authenticationClient.Setup(x => x.GetUserInfoByPhoneOrEmailAsync(It.IsAny<string>(), It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new UserInformationModel()
            {
                CompanyId = userCompanyId,
                Phone = phone
            });
        
        _traceIdAccessor.SetupGet(x => x.TraceId).Returns(traceIdResponse);

        var requestCallBack = new NewInvoiceConversationRequest();
        var traceIdCallBack = string.Empty;
        _sqs.Setup(x => x.SendAsync(It.IsAny<NewInvoiceConversationRequest>(),
                It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Callback<NewInvoiceConversationRequest, string, CancellationToken>((e, a, c) =>
            {
                requestCallBack = e;
                traceIdCallBack = a;
            });
        _companiesClient.Setup(x => x.GetCompaniesByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel[]
            {
                new() {Id = userCompanyId, BankAccounts = new []{userBankAccountId}},
                new() {Id = supplierCompanyId, Settings = new CompanySettingsModel(){ CardPricingPackageId = CompanyConstants.PricingOptOut}}
            });
        _companiesClient.Setup(x => x.GetBankAccountsByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new BankAccountModel[]
            {
                new() {Id = userBankAccountId, PaymentMethodType = CompanyConstants.CardPaymentType}
            });
        
        var service = GetService();
        await service.ProcessAsync(invoiceRequest);
        
        requestCallBack.Phone.Should().BeEmpty();
        requestCallBack.DefaultNotificationPhoneList.Count.Should().Be(1);
        requestCallBack.DefaultNotificationPhoneList.Should().Contain(phone);
        traceIdCallBack.Should().BeEquivalentTo(traceIdResponse);
        _loggerMock.VerifyLogging(LogLevel.Warning, Times.Never());
        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());
        _sqs.Verify(x => x.SendAsync(It.IsAny<NewInvoiceConversationRequest>(),
            It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once());
    }
}