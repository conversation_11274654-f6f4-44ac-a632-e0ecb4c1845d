#nullable enable
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using System.Text.Json.Serialization;
using Amazon;
using Amazon.Extensions.NETCore.Setup;
using Amazon.KeyManagementService;
using Amazon.Lambda.Core;
using Amazon.Lambda.SQSEvents;
using Amazon.SecretsManager;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Common.Extensions.Constants;
using BlueTape.LambdaBase;
using BlueTape.Notifications.Invoicing.Abstractions;
using BlueTape.Notifications.Invoicing.Extensions;
using BlueTape.Notifications.Invoicing.Models;
using BlueTape.Notifications.Invoicing.Options;
using BlueTape.Notifications.Invoicing.Services;
using BlueTape.Services.AWSMessaging.Extensions;
using BlueTape.Services.Utilities.AspNetCore;
using BlueTape.Services.Utilities.AWS;
using BlueTape.Services.Utilities.Configuration;
using BlueTape.Services.Utilities.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Context;


// Assembly attribute to enable the Lambda function's JSON input to be converted into a .NET class.
[assembly: LambdaSerializer(typeof(Amazon.Lambda.Serialization.SystemTextJson.DefaultLambdaJsonSerializer))]

namespace BlueTape.Notifications.Invoicing;

[ExcludeFromCodeCoverage]
// ReSharper disable once UnusedType.Global
public class Function : FunctionBase
{
    private ILogger<Function> _logger => ServiceProvider.GetRequiredService<ILogger<Function>>();
    private ITraceIdAccessor _traceIdAccessor => ServiceProvider.GetRequiredService<ITraceIdAccessor>();
    private INotificationProcessor _notificationProcessor => ServiceProvider.GetRequiredService<INotificationProcessor>();

    private readonly JsonSerializerOptions _serializerOptions = new(JsonSerializerDefaults.Web)
    {
        Converters = { new JsonStringEnumConverter() },
        WriteIndented = false
    };

    // ReSharper disable once UnusedMember.Global
    public async Task Handler(SQSEvent evnt, ILambdaContext context)
    {
        if (evnt?.Records == null)
            return;

        foreach (var message in evnt.Records.Where(message => !string.IsNullOrWhiteSpace(message?.Body)))
        {
            _traceIdAccessor.EnsureCorrelated(message);
            
            using (GlobalLogContext.PushProperty(Constants.Constants.Method, Constants.Constants.SqsEvent))
            using (GlobalLogContext.PushProperty(TracingConstants.BlueTapeCorrelationId, _traceIdAccessor.TraceId))
            {
                _logger.LogInformation("Accepted NodeJS message for new invoice. Message body: {body}", message.Body);

                await ProcessAsync(message);

                _logger.LogInformation("Finished event bridge event handling");
            }
        }

        Log.CloseAndFlush();
    }

    public async Task ProcessAsync(SQSEvent.SQSMessage message, CancellationToken token = default)
    {
        var body = GetBody(message);
        if (body is null || string.IsNullOrEmpty(body.InvoiceId))
        {
            _logger.LogWarning("Encountered unsupported message {@message}", message);
            return;
        }

        await _notificationProcessor.ProcessAsync(body, token);
    }

    private NewInvoiceRequest? GetBody(SQSEvent.SQSMessage message)
    {
        return JsonSerializer.Deserialize<NewInvoiceRequest>(message.Body, _serializerOptions);
    }

    protected override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        services.AddOptions();
        services.Configure<BlueTapeOptions>(configuration.GetSection(nameof(BlueTapeOptions)));
        services.Configure<ServicesOptions>(configuration.GetSection(ServicesOptions.SectionName));
        services.Configure<NotificationQueueOptions>(configuration.GetSection(nameof(NotificationQueueOptions)));

        services.AddTypedHttpClient<ICompaniesClient, CompaniesClient>();
        services.AddTypedHttpClient<IInvoicesClient, InvoicesClient>();
        services.AddTypedHttpClient<IAuthenticationClient, AuthenticationClient>();
        services.AddScoped<INotificationProcessor, NotificationProcessor>();

        services.AddSqsEndpoint<NewInvoiceConversationRequest, NotificationQueueOptions>
            (options => options.InvoiceNotificationTemplateTopicName);

        services.AddTransient<RequestResponseLoggingDelegatingHandler>();
        services.AddScoped<ITraceIdAccessor, LambdaTraceIdAccessor>();
        
        services.AddDefaultAWSOptions(configuration.GetAWSOptions());
        services.AddTransient<IConfigurationService, ConfigurationService>();
        services.AddSingleton<ISecretsManagerService, AwsSecretsManagerService>();
        services.AddAWSService<IAmazonSecretsManager>(new AWSOptions { Region = RegionEndpoint.USWest1 });
        services.AddAWSService<IAmazonKeyManagementService>(new AWSOptions { Region = RegionEndpoint.USWest1 });
    }
}
