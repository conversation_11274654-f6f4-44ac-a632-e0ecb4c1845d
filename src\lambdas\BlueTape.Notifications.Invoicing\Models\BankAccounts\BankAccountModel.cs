﻿using System.Text.Json.Serialization;

namespace BlueTape.Notifications.Invoicing.Models.BankAccounts;

public class BankAccountModel
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = null!;
    
    [JsonPropertyName("accountHolderName")]
    public string? AccountHolderName { get; set; }
    
    [JsonPropertyName("accountName")]
    public string? AccountName { get; set; }
    
    [JsonPropertyName("accountType")]
    public string? AccountType { get; set; }

    [JsonPropertyName("finicityHistorySyncDone")]
    public bool? FinicityHistorySyncDone { get; set; }
    
    [JsonPropertyName("isDeactivated")]
    public bool? IsDeactivated { get; set; }
    
    [JsonPropertyName("isManualEntry")]
    public bool? IsManualEntry { get; set; }
    
    [JsonPropertyName("isPrimary")]
    public bool? IsPrimary { get; set; }
    
    [JsonPropertyName("name")]
    public string? Name { get; set; }
    
    [JsonPropertyName("paymentMethodType")]
    public string? PaymentMethodType { get; set; }
    
    [JsonPropertyName("routingNumber")]
    public string? RoutingNumber { get; set; }

    [JsonPropertyName("status")]
    public string? Status { get; set; }

    [JsonPropertyName("thirdPartyId")]
    public string? ThirdPartyId { get; set; }
    
    [JsonPropertyName("voidedCheck")]
    public string? VoidedCheck { get; set; }
    
    [JsonPropertyName("accountNumber")]
    public AccountNumberModel? AccountNumber { get; set; }
    
    [JsonPropertyName("billingAddress")]
    public BillingAddressModel? BillingAddress { get; set; }
}