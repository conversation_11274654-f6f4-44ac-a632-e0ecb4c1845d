﻿using System.Reflection;
using Amazon.SimpleNotificationService;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.AzureKeyVault.Services;
using BlueTape.EmailSender.Extensions;
using BlueTape.Integrations.Aion.AzureTableStorage.DI;
using BlueTape.Notification.Sender.DI;
using BlueTape.NotificationService.Application.Abstractions;
using BlueTape.NotificationService.Application.Mapper;
using BlueTape.NotificationService.Application.Senders.Email.Extensions;
using BlueTape.NotificationService.Application.Services;
using BlueTape.NotificationService.DataAccess.Mongo.DI;
using BlueTape.NotificationService.Domain.Options;
using BlueTape.SNS.SlackNotification.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SendGrid;

namespace BlueTape.NotificationService.Application.DI
{
    public static class DependencyRegistrar
    {
        public static void AddApplicationDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddAutoMapper(typeof(ApplicationProfile).GetTypeInfo().Assembly);

            services.AddSingleton<IKeyVaultService, KeyVaultService>();
            services.AddSingleton<IMemoryCache, MemoryCache>();
            services.AddTransient<IDateProvider, DateProvider>();

            services.AddSingleton<ISendGridClient, SendGridClient>(_ =>
                new SendGridClient(config.GetSection("LP-SENDGRID-API-KEY").Value!));

            services.AddTransient<INotificationProcessor, NotificationProcessor>();
            services.AddTransient<IS3FileService, S3FileService>();

            services.AddSnsSlackNotifications();
            services.AddTransient<ISlackNotificationService, SlackNotificationService>();
            services.AddAWSService<IAmazonSimpleNotificationService>();
            services.AddEmailService();

            services.Configure<SlackNotificationOptions>(config.GetSection(SlackNotificationOptions.SectionName));

            services.AddAzureDataTableDependencies(config);
            services.AddMongoDataAccessDependencies();
            services.AddNotificationEmailService();
            services.AddAzureSystemNotifications();
        }
    }
}
