﻿using System.Diagnostics.CodeAnalysis;
using System.Text;
using BlueTape.Notifications.Invoicing.Abstractions;
using BlueTape.Notifications.Invoicing.Constants;
using BlueTape.Notifications.Invoicing.Extensions;
using BlueTape.Notifications.Invoicing.Models.Users;
using BlueTape.Notifications.Invoicing.Options;
using BlueTape.Services.Utilities.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.Notifications.Invoicing.Services;

[ExcludeFromCodeCoverage(Justification = "Will be covered when we will be more close to nuget. Next phase")]
public class AuthenticationClient : IAuthenticationClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<AuthenticationClient> _logger;

    public AuthenticationClient(HttpClient httpClient, IOptions<ServicesOptions> options, ILogger<AuthenticationClient> logger,
        IConfigurationService configurationService)
    {
        _httpClient = httpClient;
        _logger = logger;
        _httpClient.BaseAddress = new Uri(options.Value.AuthenticationApi);
        
        var apiKey = configurationService.GetByKey(ConfigurationKeys.AuthenticationApiKey).GetAwaiter().GetResult();
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"apiKey {apiKey}");
    }
    
    public async Task<UserInformationModel?> GetUserInfoByPhoneOrEmailAsync(string? phone, string? email, CancellationToken cancellationToken)
    {
        var str = new StringBuilder();
        str.Append($"users/userinfo");
        if (string.IsNullOrEmpty(phone)) phone = $"{nameof(phone)}";
        if (string.IsNullOrEmpty(email)) email = $"{nameof(email)}";
        
        str.Append($"/phone/{phone}");
        str.Append($"/email/{email}");
        
        var requestUri = str.ToString();
        
        try
        {
            var response = await _httpClient.GetAsync(requestUri, cancellationToken);
            
            _logger.LogInformation("Received GetUserInfoByPhoneOrEmailAsync Authentication API response {@response}", response);
            response.EnsureSuccessStatusCode();
            return await response.DeserializeResponse<UserInformationModel>(cancellationToken);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Failed at reading data from Authentication API at {baseAddress}/{requestUri}", _httpClient.BaseAddress, requestUri);
            throw;
        }
    }
}
