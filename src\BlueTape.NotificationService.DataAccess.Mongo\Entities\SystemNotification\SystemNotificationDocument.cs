using BlueTape.Notification.Sender.Enums;
using BlueTape.SNS.SlackNotification.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;

[BsonIgnoreExtraElements]
public class EmailReceiverData
{
    [BsonElement("email")]
    public string Email { get; set; } = null!;

    [BsonElement("name")]
    public string Name { get; set; } = null!;
}

[BsonIgnoreExtraElements]
public class S3AttachmentReferenceDocument
{
    [BsonElement("bucketName")]
    public string BucketName { get; set; } = null!;

    [BsonElement("key")]
    public string Key { get; set; } = null!;

    [BsonElement("fileName")]
    public string? FileName { get; set; }

    [BsonElement("contentType")]
    public string? ContentType { get; set; }
}

[BsonIgnoreExtraElements]
public class EmailPayloadDocument
{
    [BsonElement("receivers")]
    public List<EmailReceiverData> Receivers { get; set; } = null!;

    [BsonElement("copyTo")]
    public List<EmailReceiverData> CopyTo { get; set; } = null!;
    
    [BsonElement("from")]
    public EmailReceiverData? From { get; set; } = null!;
    
    [BsonElement("subject")]
    public string Subject { get; set; } = null!;

    [BsonElement("templateId")]
    public string? TemplateId { get; set; } = null!;
    
    [BsonElement("templatePayload")]
    public string? TemplatePayload { get; set; } = null!;
    
    [BsonElement("senderCompanyId")]
    public string? SenderCompanyId { get; set; }
    
    [BsonElement("html")]
    public string? Html { get; set; } = null!;

    [BsonElement("s3AttachmentReferences")]
    public List<S3AttachmentReferenceDocument>? S3AttachmentReferences { get; set; }
}

[BsonIgnoreExtraElements]
public class SlackPayloadDocument
{
    [BsonElement("message")]
    public string Message { get; set; } = null!;

    [BsonElement("eventName")]
    public string EventName { get; set; } = null!;

    [BsonElement("eventLevel")]
    public EventLevel EventLevel { get; set; }
}

[BsonIgnoreExtraElements]
public class BlueTapeBackOfficePayloadDocument
{
    [BsonElement("templateId")]
    public string? TemplateId { get; set; } = null!;

    [BsonElement("subject")]
    public string Subject { get; set; } = null!;

    [BsonElement("templatePayload")]
    public string? TemplatePayload { get; set; } = null!;

    [BsonElement("html")]
    public string? Html { get; set; } = null!;

    [BsonElement("slackNotification")]
    public SlackPayloadDocument? SlackNotification { get; set; } = null!;
}

[BsonIgnoreExtraElements]
public class UserUiReviewPayloadDocument
{
    [BsonElement("notificationLevel")]
    [BsonRepresentation(BsonType.String)]
    public NotificationLevel NotificationLevel { get; set; }

    [BsonElement("description")]
    public string Description { get; set; } = null!;
}

[BsonIgnoreExtraElements]
public class NotificationChannelDocument<T>
{
    [BsonElement("payload")]
    public T Payload { get; set; } = default!;

    [BsonElement("isDelivered")]
    public bool IsDelivered { get; set; }

    [BsonElement("deliveryId")]
    public string DeliveryId { get; set; } = null!;
}

[BsonIgnoreExtraElements]
public class SystemNotificationDocument
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    [BsonElement("_id")]
    public string Id { get; set; } = null!;

    [BsonElement("source")]
    [BsonRepresentation(BsonType.String)]
    public NotificationSource Source { get; set; }

    [BsonElement("name")]
    public string NotificationName { get; set; } = null!;

    [BsonElement("description")]
    public string Description { get; set; } = null!;

    [BsonElement("companyId")]
    public string? CompanyId { get; set; } = null!;

    [BsonElement("traceId")]
    public string TraceId { get; set; } = null!;

    [BsonElement("referenceId")]
    public List<string>? ReferenceIds { get; set; } = null!;

    [BsonElement("createdAt")]
    public DateTime? CreatedAt { get; set; }

    [BsonElement("updatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [BsonElement("userUiReviewDelivery")]
    public List<NotificationChannelDocument<UserUiReviewPayloadDocument>>? UserUiReviewDelivery { get; set; }

    [BsonElement("emailDelivery")]
    public List<NotificationChannelDocument<EmailPayloadDocument>>? EmailDelivery { get; set; }

    [BsonElement("blueTapeBackOfficeDelivery")]
    public List<NotificationChannelDocument<BlueTapeBackOfficePayloadDocument>>? BlueTapeBackOfficeDelivery { get; set; }
}