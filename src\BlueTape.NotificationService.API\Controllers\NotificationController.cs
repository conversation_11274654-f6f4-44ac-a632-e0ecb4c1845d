﻿using AutoMapper;
using BlueTape.Notification.Sender.APIModels;
using BlueTape.Notification.Sender.Enums;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.NotificationService.Application.Abstractions;
using BlueTape.NotificationService.DataAccess.Mongo.Abstractions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;

namespace BlueTape.NotificationService.API.Controllers;

[ExcludeFromCodeCoverage(Justification = "Controllers are not required to be tested at the moment")]
[Authorize]
[ApiController]
[Route("[controller]")]
public class NotificationController(
    ISystemNotificationRepository systemNotificationRepository,
    INotificationProcessor notificationProcessor,
    IMapper mapper) : ControllerBase
{
    [HttpGet("company/{companyId}")]
    public async Task<IActionResult> GetByCompanyIdAndReferenceId(
        [FromRoute] string companyId,
        [FromQuery] List<string>? referenceIds,
        [FromQuery] NotificationSource? source,
        CancellationToken ct)
    {
        var results = await systemNotificationRepository
            .FetchNotificationsByCompanyAndFilters(companyId, ct, referenceIds?.ToList() ?? [], true, source);

        return Ok(mapper.Map<List<SystemNotificationModel>>(results));
    }

    [HttpPost("Process")]
    public async Task<IActionResult> AddNotificationWithS3Attachments(CancellationToken ct)
    {
        var dynamicEmailData = new
        {
            customerName = "John Doe Customer",
            invoiceNumber = "inv-K490124",
            amount = "500$",
            totalProcessingAmount = "500$",
            paymentType = "ACH"
        };

        var document = new SystemNotificationDto
        {
            TraceId = $"NotificationService-{Guid.NewGuid()}",
            CompanyId = "BlueTapeTest",
            ReferenceIds = new List<string> { "BlueTapeTest" },
            Source = NotificationSource.Payment,
            Description = "Payment notification with S3 attachments",
            NotificationName = "PaymentNotificationWithAttachments",
            EmailDelivery = new List<NotificationChannelDto<EmailPayloadDto>>
            {
                new()
                {
                   Payload = new EmailPayloadDto
                   {
                       TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                       Receivers = new List<EmailReceiverDataDto>()
                       {
                           new()
                           {
                               Email = "<EMAIL>",
                               Name = "John Doe"
                           }
                       },
                       CopyTo = new List<EmailReceiverDataDto>(),
                       TemplateId = "d-d5f0a234bf664fac82988f48f2d0e701",
                       Subject = "Payment Notification with Attachments",
                       SenderCompanyId = "TestMerchant",
                       S3AttachmentReferences = new List<S3AttachmentReferenceDto>
                       {
                           new()
                           {
                               BucketName = "qa.uw1.linqpal-user-assets",
                               Key = "instructions/down_payment_instructions.pdf",
                               FileName = "Invoice_K490124.pdf",
                               ContentType = "application/pdf"
                           },
                           new()
                           {
                               BucketName = "qa.uw1.linqpal-user-assets",
                               Key = "instructions/down_payment_instructions.pdf",
                               FileName = "Payment_Receipt.pdf",
                               ContentType = "application/pdf"
                           }
                       }
                   }
                }
            }
        };
        await notificationProcessor.Process(document, ct);
        return Ok();
    }
}