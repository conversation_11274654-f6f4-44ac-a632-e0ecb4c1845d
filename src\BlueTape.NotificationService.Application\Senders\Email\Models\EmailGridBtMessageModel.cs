﻿using SendGrid.Helpers.Mail;

namespace BlueTape.NotificationService.Application.Senders.Email.Models;

public class EmailGridBtMessageModel<T> where T : class
{
    public string TemplateId { get; set; }
    public string ToName { get; set; }
    public string ToEmail { get; set; }
    public T TemplateData { get; set; }
    public string FromEmail { get; set; }
    public string FromName { get; set; }
    public string BccEmails { get; set; }
    public IEnumerable<Attachment> Attachments { get; set; } = Enumerable.Empty<Attachment>();
}
