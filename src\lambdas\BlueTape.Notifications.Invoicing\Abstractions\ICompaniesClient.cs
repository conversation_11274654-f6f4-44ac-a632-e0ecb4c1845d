﻿using BlueTape.Notifications.Invoicing.Models.BankAccounts;
using BlueTape.Notifications.Invoicing.Models.Companies;
using BlueTape.Notifications.Invoicing.Models.Customers;

namespace BlueTape.Notifications.Invoicing.Abstractions;

public interface ICompaniesClient
{
    Task<CompanyModel?> GetCompanyByIdAsync(string id, CancellationToken cancellationToken);
    Task<CompanyModel[]?> GetCompaniesByIdsAsync(string[] ids, CancellationToken cancellationToken);
    Task<CustomerModel?> GetCustomerByIdAsync(string id, CancellationToken cancellationToken);
    Task<BankAccountModel?> GetBankAccountByIdAsync(string id, CancellationToken cancellationToken);
    Task<BankAccountModel[]?> GetBankAccountsByIdsAsync(string?[] ids, CancellationToken cancellationToken);
    Task<CustomerModel[]?> GetCustomerBillingContactsAsync(string id, CancellationToken cancellationToken);
    Task<CustomerModel[]?> GetCustomerByIdsAsync(string[] ids, CancellationToken cancellationToken);
}