using System.Diagnostics.CodeAnalysis;
using BlueTape.Notifications.Invoicing.Abstractions;
using BlueTape.Notifications.Invoicing.Extensions;
using BlueTape.Notifications.Invoicing.Models.Invoices;
using BlueTape.Notifications.Invoicing.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.Notifications.Invoicing.Services;

[ExcludeFromCodeCoverage(Justification = "Will be covered when we will be more close to nuget. Next phase")]
public class InvoicesClient : IInvoicesClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<InvoicesClient> _logger;

    public InvoicesClient(HttpClient httpClient, IOptions<ServicesOptions> options, ILogger<InvoicesClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _httpClient.BaseAddress = new Uri(options.Value.InvoicesApi);
    }
    
    public async Task<InvoiceModel?> GetByIdAsync(string id, CancellationToken cancellationToken)
    {
        var requestUri = $"invoices/{id}";
        try
        {
            var response = await _httpClient.GetAsync(requestUri, cancellationToken);
            _logger.LogInformation("Received GetByIdAsync Invoice API response {@response}", response);
            response.EnsureSuccessStatusCode();
            return await response.DeserializeResponse<InvoiceModel>(cancellationToken);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Failed at reading data from Invoices API at {baseAddress}/{requestUri}", _httpClient.BaseAddress, requestUri);
            throw;
        }
    }    
}
