service: dotnet-notification-service
useDotenv: true
frameworkVersion: ^3.9.0

plugins:
  - serverless-dotenv-plugin
  - serverless-offline
  - serverless-plugin-warmup
  - serverless-prune-plugin
package:
  individually: true

custom: ${file(_custom.yml):global}

provider:
  name: aws
  runtime: dotnet6
  region: us-west-1
  timeout: 600
  stage: ${env:STAGE, 'dev'}
  tracing:
    lambda: true
  deploymentBucket:
    name: ${self:custom.notificationService.deploymentBucket.${env:STAGE}}
  iam:
    role: ${self:custom.notificationService.role.${env:STAGE}}
  vpc:
    securityGroupIds: ${self:custom.notificationService.vpcGroup.${env:STAGE}}
    subnetIds: ${self:custom.notificationService.vpcSubnet.${env:STAGE}}
  environment:
    LP_AWS_ACCOUNT: "${env:LP_AWS_ACCOUNT}"
    ASPNETCORE_ENVIRONMENT: "${env:ASPNETCORE_ENVIRONMENT}"
    LOGZIO_TOKEN: ${env:LOGZIO_TOKEN}
    PROJECT_NAME: POT

functions:
  NotificationsInvoicing:
    handler: BlueTape.Notifications.Invoicing::BlueTape.Notifications.Invoicing.Function::Handler
    reservedConcurrency: 1
    events:
      - sqs:
          arn: !GetAtt NotificationServiceFifoQueue.Arn
    package:
      artifact: ${env:LAMBDA_BRIDGE_PACKAGE_LOCATION}

resources:
  Resources:
    NotificationServiceFifoQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: notification-service-${env:STAGE}.fifo
        ContentBasedDeduplication: true
        VisibilityTimeout: 600
        FifoQueue: true
        DelaySeconds: 0
        RedrivePolicy:
          deadLetterTargetArn: !GetAtt NotificationServiceDeadLetterFifoQueue.Arn
          maxReceiveCount: 5

    NotificationServiceDeadLetterFifoQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: notification-service-dead-letter-queue-${env:STAGE}.fifo
        ContentBasedDeduplication: false
        FifoQueue: true
        VisibilityTimeout: 30

    EventBridge:
      Type: AWS::Events::EventBus
      Properties:
        Name: ${self:custom.notificationService.eventBridge.eventBusName.${env:STAGE}}
    
    EventRule:
      Type: AWS::Events::Rule
      Properties:
        EventBusName: !Ref EventBridge
        EventPattern:
          account:
            - !Sub '${AWS::AccountId}'          
          source:
            - "linqpal"
          detail-type:
            - 'pay-over-text'
        Targets:
          - Arn: !GetAtt NotificationServiceFifoQueue.Arn
            Id: linqpal-pay-over-text-${env:STAGE}
            SqsParameters:
              MessageGroupId: linqpal-pay-over-text-${env:STAGE}
            InputTransformer:
              InputPathsMap:
                "invoiceId": "$.detail.invoiceId"
                "phone": "$.detail.phone"
              InputTemplate: |
                {
                  "invoiceId": <invoiceId>,
                  "phone": <phone>
                }              

    EventBusPolicy:
      Type: AWS::Events::EventBusPolicy
      Properties:
        EventBusName: !Ref EventBridge
        StatementId: ${self:custom.notificationService.eventBridge.policyStatement.${env:STAGE}}
        Statement:
          Effect: "Allow"
          Principal:
            AWS: ${self:custom.notificationService.role.${env:STAGE}}
          Action: "events:PutEvents"
          Resource: !GetAtt EventBridge.Arn

    EventBridgeToToSqsPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Statement:
            - Effect: Allow
              Principal:
                Service: events.amazonaws.com
              Action: sqs:SendMessage
              Resource: !GetAtt NotificationServiceFifoQueue.Arn
              Condition:
                ArnEquals:
                  aws:SourceArn: !GetAtt EventRule.Arn
        Queues:
          - !Ref NotificationServiceFifoQueue

