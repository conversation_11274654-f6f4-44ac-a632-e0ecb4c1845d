﻿using System.Runtime.Serialization;
using SendGrid;

namespace BlueTape.NotificationService.Application.Senders.Email;

[Serializable]
public class SendGridClientException : Exception, ISerializable
{
    public Response Response { get; private set; }

    public SendGridClientException(string message) : base(message)
    {
    }

    public SendGridClientException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {

    }

    public SendGridClientException(string message, Response response)
        : base(message)
    {
        Response = response;
    }
}



