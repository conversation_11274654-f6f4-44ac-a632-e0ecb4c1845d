﻿using BlueTape.Common.ExceptionHandling.Exceptions;
using System.Diagnostics.CodeAnalysis;
using System.Security.Claims;
using System.Text.Encodings.Web;
using BlueTape.NotificationService.Domain.Constants;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;

namespace BlueTape.NotificationService.API.Handlers;

[ExcludeFromCodeCoverage(Justification = "Will be covered when moved to package")]
public class ApiKeyHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ApiKeyHandler> _logger;

    public ApiKeyHandler(
        IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        IConfiguration configuration
    ) : base(options, logger, encoder)
    {
        _configuration = configuration;
        _logger = logger.CreateLogger<ApiKeyHandler>();
    }

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var principal = new ClaimsPrincipal(new ClaimsIdentity(Enumerable.Empty<Claim>(), ConfigurationKeys.ApiKeyAuthScheme));
        var ticket = new AuthenticationTicket(principal, Scheme.Name);
        
        if (!Request.Headers.TryGetValue(ConfigurationKeys.ApiKeyName, out var extractedApiKey))
        {
            return Task.FromResult(AuthenticateResult.NoResult());
        }

        var apiKey = _configuration[ConfigurationKeys.NotificationApiKey] ??
                     throw new VariableNullException(ConfigurationKeys.NotificationApiKey);
        if (!apiKey!.Equals(extractedApiKey))
        {
            _logger.LogWarning("Authentication failed");
            return Task.FromResult(AuthenticateResult.Fail("Authentication failed"));
        }

        return Task.FromResult(AuthenticateResult.Success(ticket));
    }
}
