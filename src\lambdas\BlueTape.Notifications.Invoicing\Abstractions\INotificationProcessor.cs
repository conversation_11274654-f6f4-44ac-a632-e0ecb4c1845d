﻿using BlueTape.Notifications.Invoicing.Models;
using BlueTape.Notifications.Invoicing.Models.Invoices;

namespace BlueTape.Notifications.Invoicing.Abstractions;

public interface INotificationProcessor
{
    Task ProcessAsync(NewInvoiceRequest invoiceRequest, CancellationToken cancellationToken = default);
    Task<List<string>> GetPhonesForSimpleNotification(InvoiceModel invoice, CancellationToken cancellationToken);
}