# Service Bus Namespace
resource "azurerm_servicebus_namespace" "sbn" {
  name                = "service-bus-namespace-${var.application_name.full}-${var.environment}"
  location            = var.resource_group_location
  resource_group_name = var.resource_group_name
  sku                 = "Standard"

  tags = {
    environment = title(var.environment)
    source      = "Terraform"
    app         = title(var.application_name.full)
    CreatedOn   = formatdate("YYYY-MM-DD hh:mm ZZZ", timestamp())
    Type        = "Microsoft Azure Service Bus Namespace"
  }

  lifecycle {
    ignore_changes = [
      tags["CreatedOn"]
    ]
  }

}

resource "azurerm_servicebus_queue" "sbqns" {
  name         = "notificationqueue-${var.environment}"
  namespace_id = azurerm_servicebus_namespace.sbn.id
  max_delivery_count = 1
}

resource "azurerm_key_vault_secret" "notification-bus-job-queue-secret" {
  name         = "${var.notification_queue_connection}"
  value        = azurerm_servicebus_queue_authorization_rule.notification-bus-read-write-rule.primary_connection_string
  key_vault_id = var.key_vault_id
}

resource "azurerm_key_vault_secret" "notification-bus-job-queue-name-secret" {
  name         = "${var.notification_queue_name}"
  value        = azurerm_servicebus_queue.sbqns.name
  key_vault_id = var.key_vault_id
}

resource "azurerm_servicebus_queue_authorization_rule" "notification-bus-read-write-rule" {
  name     = "${var.application_name.slug}-bus-queue-connection-auth-rule"
  queue_id = azurerm_servicebus_queue.sbqns.id
  listen = true
  send = true
}