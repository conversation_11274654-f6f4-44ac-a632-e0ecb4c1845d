{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"AWSSDK": "Warning", "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "BlueTape.Services.Utilities.AWS": "Warning", "Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.DataProtection": "Error", "Microsoft.EntityFrameworkCore": "Warning", "System.Net.Http.HttpClient": "Information"}}}, "NotificationQueueOptions": {"InvoiceNotificationTemplateTopicName": "user-interaction-service-invoicing-prod.fifo"}, "BlueTapeOptions": {"AwsSecretName": "bluetape_keys_prod"}}