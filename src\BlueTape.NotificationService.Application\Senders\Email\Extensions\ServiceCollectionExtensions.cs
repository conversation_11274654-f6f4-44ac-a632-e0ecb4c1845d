using System.Diagnostics.CodeAnalysis;
using BlueTape.NotificationService.Application.Senders.Email.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using SendGrid;
using SendGrid.Extensions.DependencyInjection;

namespace BlueTape.NotificationService.Application.Senders.Email.Extensions;

[ExcludeFromCodeCoverage]
public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddNotificationEmailService(this IServiceCollection services) =>
        AddEmailServiceInternal(services,
            (_, options) => { options.ApiKey = Environment.GetEnvironmentVariable("SENDGRID_API_KEY"); });

    private static IServiceCollection AddEmailServiceInternal(IServiceCollection services,
        Action<IServiceProvider, SendGridClientOptions> configureOptions)
    {
        services.AddSendGrid(configureOptions);
        services.AddSingleton<ISendGridNotificationClient, SendGridNotificationClient>();

        return services;
    }
}