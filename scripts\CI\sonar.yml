.sonarcloud-check:base:
   stage: sonar 
   variables:
     SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
     GIT_DEPTH: "0"
     PROJECT_KEY: "linqpal1_notificationservice"
     ORGANIZATION: "bluetape"
     EXCLUDE_FROM_COVERAGE: "**/Constants/**/*, **/Models/**/*, **/Options/**/*, **/Extensions/**/*"
   cache:
     key: "${CI_JOB_NAME}"
     paths:
       - .sonar/cache
   before_script:
    - 'dotnet restore $PATH_TO_SLN --packages $NUGET_PACKAGES_DIRECTORY'
   script: 
    - apt-get update
    - apt-get install --yes openjdk-11-jre
    - dotnet tool restore
    - dotnet sonarscanner begin /k:"$PROJECT_KEY" /o:"$ORGANIZATION" /d:sonar.login="$SONAR_TOKEN" /d:sonar.host.url="$SONAR_HOST_URL" /d:sonar.coverage.exclusions="$EXCLUDE_FROM_COVERAGE" /d:sonar.cs.opencover.reportsPaths="**/TestResults/**/coverage.opencover.xml" -d:sonar.cs.vstest.reportsPaths="**/TestResults/*.trx"
    - dotnet build --no-restore --configuration Release $PATH_TO_SLN
    - dotnet test $PATH_TO_SLN --no-build --no-restore --configuration Release --verbosity normal --logger trx --collect:"XPlat Code Coverage" -- DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Format=opencover
    - dotnet sonarscanner end /d:sonar.login="$SONAR_TOKEN"
   allow_failure: true

sonarcloud-check:dev:
   extends: .sonarcloud-check:base
   only:
    !reference [ "building:api", only ]
   when: manual

sonarcloud-check:beta:
   extends: .sonarcloud-check:base
   only:
    !reference [ "building:api", only ]
   when: manual

sonarcloud-check:qa:
   extends: .sonarcloud-check:base
   only:
    !reference [ "building:api", only ]
   when: manual

sonarcloud-check:prod:
   extends: .sonarcloud-check:base
   only:
    !reference [ "building:prod", only ]
   when: manual

sonarcloud-check:main:
   extends: .sonarcloud-check:base
   only:
    - merge_requests
    - main   
