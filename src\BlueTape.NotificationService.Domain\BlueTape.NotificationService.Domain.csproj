﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="BlueTape.Integrations.Aion.AzureTableStorage" Version="1.0.9" />
        <PackageReference Include="BlueTape.ServiceBusMessaging" Version="1.0.9" />
        <PackageReference Include="BlueTape.SNS" Version="1.0.2" />
        <PackageReference Include="BlueTape.AWSS3" Version="1.1.5" />
        <PackageReference Include="BlueTape.AWSMessaging" Version="2.0.5" />
        <PackageReference Include="BlueTape.EmailSender" Version="3.0.7" />
        <PackageReference Include="BlueTape.Utilities" Version="1.4.5" />
        <PackageReference Include="BlueTape.AzureKeyVault" Version="1.0.3" />
        <PackageReference Include="BlueTape.Common.ExceptionHandling" Version="1.0.8" />
        <PackageReference Include="BlueTape.Logging" Version="1.0.0" />
        <PackageReference Include="BlueTape.Common.Extensions" Version="1.1.1" />

        <PackageReference Include="BlueTape.Notification.Sender" Version="1.0.4" />
    </ItemGroup>
    
    <ItemGroup>
        <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.0" />
        <PackageReference Include="Azure.Identity" Version="1.10.4" />
        <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.17.1" />
        <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.5.0" />
    </ItemGroup>
    
    <ItemGroup>
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
        <PackageReference Include="AutoMapper" Version="12.0.1" />
        <PackageReference Include="SendGrid" Version="9.29.2" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
        <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
        <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
    </ItemGroup>
    
</Project>
