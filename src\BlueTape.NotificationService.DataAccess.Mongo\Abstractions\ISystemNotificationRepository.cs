using BlueTape.Notification.Sender.Enums;
using BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;

namespace BlueTape.NotificationService.DataAccess.Mongo.Abstractions;

public interface ISystemNotificationRepository
{
    Task<List<SystemNotificationDocument>> FetchNotificationsByCompanyAndFilters(
        string companyId,
        CancellationToken ct,
        List<string>? referenceIds = null,
        bool returnAll = true,
        NotificationSource? notificationSource = null,
        int pageNumber = 1,
        int pageSize = 10);
    Task<SystemNotificationDocument> AddAsync(SystemNotificationDocument systemNotification, CancellationToken ct);
    Task UpdateIsDeliveredByDeliveryIdsAsync(string notificationId, List<string> deliveryIds, CancellationToken ct);
}