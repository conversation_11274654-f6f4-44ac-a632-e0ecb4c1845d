﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization;

namespace BlueTape.NotificationService.DataAccess.Mongo.Serializers;

public class CustomStringSerializer : IBsonSerializer
{
    public Type ValueType { get; } = typeof(string);

    public object? Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        switch (context.Reader.CurrentBsonType)
        {
            case BsonType.String:
                return context.Reader.ReadString();
            case BsonType.Int32:
                return context.Reader.ReadInt32().ToString();
            case BsonType.Null:
                context.Reader.ReadNull();
                return null;
            default:
                throw new NotImplementedException($"No implementation to deserialize {context.Reader.CurrentBsonType}");
        }
    }

    public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, object? value)
    {
        switch (value)
        {
            case null: context.Writer.WriteNull(); break;
            case string stringValue: context.Writer.WriteString(stringValue); break;
            case int number: context.Writer.WriteInt32(number); break;
            default: throw new NotImplementedException();
        }
    }
}