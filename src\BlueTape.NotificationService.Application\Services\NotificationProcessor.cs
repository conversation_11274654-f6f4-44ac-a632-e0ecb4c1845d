using AutoMapper;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.NotificationService.Application.Abstractions;
using BlueTape.NotificationService.Application.Senders.Email.Abstractions;
using BlueTape.NotificationService.DataAccess.Mongo.Abstractions;
using BlueTape.NotificationService.DataAccess.Mongo.Entities.SystemNotification;

namespace BlueTape.NotificationService.Application.Services;

public class NotificationProcessor : INotificationProcessor
{
    private readonly IMapper _mapper;
    private readonly ISystemNotificationRepository _systemNotificationRepository;
    private readonly ISendGridNotificationClient _sendGridNotificationClient;
    private readonly ISlackNotificationService _slackNotificationService;

    public NotificationProcessor(
        IMapper mapper,
        ISystemNotificationRepository systemNotificationRepository,
        ISendGridNotificationClient sendGridNotificationClient,
        ISlackNotificationService slackNotificationService)
    {
        _mapper = mapper;
        _systemNotificationRepository = systemNotificationRepository;
        _sendGridNotificationClient = sendGridNotificationClient;
        _slackNotificationService = slackNotificationService;
    }
    
    public async Task Process(SystemNotificationDto notificationDto, CancellationToken ct)
    {
        var document = _mapper.Map<SystemNotificationDocument>(notificationDto);
        document = await _systemNotificationRepository.AddAsync(document, ct);

        var deliveredIds = new List<string>();
        
        var deliveredEmails = await ProcessEmailDelivery(document, ct);
        var deliveredBackOffice = await ProcessBlueTapeBackOfficeDelivery(document, ct);
        
        deliveredIds.AddRange(deliveredEmails);
        deliveredIds.AddRange(deliveredBackOffice);

        if (deliveredEmails.Any())
        {
            await _systemNotificationRepository.UpdateIsDeliveredByDeliveryIdsAsync(document.Id, deliveredIds, ct);
        }
    }

    private async Task<List<string>> ProcessEmailDelivery(SystemNotificationDocument notificationDto, CancellationToken ct)
    {
        var deliveredEmails = new List<string>();
        
        if (notificationDto.EmailDelivery == null || !notificationDto.EmailDelivery.Any()) return deliveredEmails;

        foreach (var email in notificationDto.EmailDelivery)
        {
            var isSuccess = await _sendGridNotificationClient.SendEmailNotification(email, ct);
            if(isSuccess) deliveredEmails.Add(email.DeliveryId);
        }

        return deliveredEmails;
    }
    
    private async Task<List<string>> ProcessBlueTapeBackOfficeDelivery(SystemNotificationDocument notificationDto, CancellationToken ct)
    {
        var deliveredEmails = new List<string>();
        
        if (notificationDto.BlueTapeBackOfficeDelivery == null || !notificationDto.BlueTapeBackOfficeDelivery.Any()) return deliveredEmails;

        foreach (var email in notificationDto.BlueTapeBackOfficeDelivery)
        {
            var isSuccess = await _sendGridNotificationClient.SendOpsTeamNotification(email, ct);
            if(isSuccess) deliveredEmails.Add(email.DeliveryId);

            if (email.Payload.SlackNotification != null)
            {
                await _slackNotificationService.NotifyOpsTeam(
                    email.Payload.SlackNotification.Message,
                    email.Payload.SlackNotification.EventName,
                    email.Payload.SlackNotification.EventLevel,
                    notificationDto.Source.ToString(),
                    notificationDto.TraceId,
                    ct);
            }
        }

        return deliveredEmails;
    }
}