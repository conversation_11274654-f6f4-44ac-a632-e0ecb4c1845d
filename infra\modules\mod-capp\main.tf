# Container App
resource "azurerm_container_app" "ca_notification" {
  name                         = "ca-${var.application_name.full}-${var.environment}"
  container_app_environment_id = var.container_app_environment_id
  resource_group_name          = var.resource_group_name
  revision_mode                = "Single"
  
  secret {    
    name = "acr-password"
    value = var.acr_password
  }
  
  secret {
    name = "client-id" 
    value = var.client_id
  }

  secret {
    name = "tenant-id"
    value = var.tenant_id
  }

  secret {
    name = "client-password"
    value = var.client_secret
  }

  secret {
    name = "lp-aws-account"
    value = var.lp_aws_account
  }
  
  secret {
    name = "aws-secret-access-key"
    value = var.aws_secret_access_key
  }

  secret {
    name = "aws-access-key-id"
    value = var.aws_access_key_id
  }

  secret {
    name = "aws-default-region"
    value = var.aws_default_region
  }

    lifecycle {
    ignore_changes = [
      secret,
      tags["CreatedOn"]
    ]
  }

  registry {
    server = var.container_registry_login_server
    username = var.container_registry_admin_username
    password_secret_name = "acr-password"
  }

  ingress {    
    allow_insecure_connections = false
    external_enabled           = true
    target_port                = 8080
    traffic_weight {
      latest_revision = true
      percentage = 100
    }
  }

  template {
    container {
      name   = "container-${var.application_name.full}-${var.environment}"
      image  = "${local.image_name}"
      cpu    = 0.25
      memory = "0.5Gi"

      env {
        name = "KEYVAULT_URI"
        value = var.key_vault_uri
      }

      env {
        name = "ASPNETCORE_ENVIRONMENT"
        value = var.environment
      }

      env {
       name = "AZURE_CLIENT_ID" 
       secret_name = "client-id"
      }

      env {
        name = "AZURE_CLIENT_SECRET"
        secret_name = "client-password"
      }

      env {
        name = "AZURE_TENANT_ID"
        secret_name = "tenant-id"
      }

      env {
        name = "AWS_ACCESS_KEY_ID"
        secret_name = "aws-access-key-id"
      }

      env {
        name = "AWS_SECRET_ACCESS_KEY"
        secret_name = "aws-secret-access-key"
      }

      env {
        name = "AWS_DEFAULT_REGION"
        secret_name = "aws-default-region"
      }

      env {
        name = "AWS_REGION"
        secret_name = "aws-default-region"
      }

      env {
        name = "LP_AWS_ACCOUNT"
        secret_name = "lp-aws-account"
      }

      readiness_probe {
        transport = "TCP"
        path = "/health"
        port      = 8080
      }
 
      liveness_probe {
        transport = "TCP"
        initial_delay = 3
        path = "/health"
        port      = 8080
      }

      startup_probe {
        transport = "TCP"
        path = "/health"
        port      = 8080
      }
    }
    min_replicas = 1
    max_replicas = 2 # Min and max should be defined per environment
  }

  tags = {
    environment = title(var.environment)
    source      = "Terraform"
    app         = title(var.application_name.full)
    CreatedOn   = formatdate("YYYY-MM-DD hh:mm ZZZ", timestamp())
    Type        = "Microsoft Azure Container App"
  }
}

locals {
  image_name = "${var.container_registry_login_server}/${var.application_name.full}:${var.image_version}"
}