.building:base:
   stage: build 
   variables:
     GIT_DEPTH: "0"
   cache:
     key: "${CI_JOB_NAME}"
     paths:
       - .build/cache
   before_script:
    - 'dotnet restore $PATH_TO_SLN --packages $NUGET_PACKAGES_DIRECTORY'
   script: 
    - apt-get update
    - apt-get -y install zip
    - export GITLAB_PACKAGE_REGISTRY_USERNAME=$GITLAB_PACKAGE_REGISTRY_USERNAME
    - export GITLAB_PACKAGE_REGISTRY_PASSWORD=$GITLAB_PACKAGE_REGISTRY_PASSWORD
    - dotnet tool restore
    - dotnet build --no-restore --configuration Release $PATH_TO_SLN

building:api:
   extends: .building:base
   only:
    - main
    - /^feature\/.*$/
    - /^bugfix\/.*$/
    - /^hotfix\/.*$/

building:prod:
   extends: .building:base
   only:
    - prod